/****************************************************************************
 * Copyright (c) 2023 - 2024 liangxiegame UNDER MIT License
 * 
 * https://qframework.cn
 * https://github.com/liangxiegame/QFramework
 * https://gitee.com/liangxiegame/QFramework
 ****************************************************************************/

using System;
using System.Collections.Generic;
using UnityEngine;

namespace QFramework
{
    #region 接口定义

    /// <summary>
    /// 分层状态接口
    /// </summary>
    public interface IHState : IState
    {
        /// <summary>
        /// 获取父状态
        /// </summary>
        IHState Parent { get; set; }
        
        /// <summary>
        /// 获取子状态机
        /// </summary>
        IHFSM SubStateMachine { get; }
        
        /// <summary>
        /// 检查是否包含子状态机
        /// </summary>
        bool HasSubStateMachine { get; }
    }

    /// <summary>
    /// 分层有限状态机接口
    /// </summary>
    public interface IHFSM
    {
        /// <summary>
        /// 启动状态机
        /// </summary>
        void Start();
        
        /// <summary>
        /// 更新状态机
        /// </summary>
        void Update();
        
        /// <summary>
        /// 固定更新状态机
        /// </summary>
        void FixedUpdate();
        
        /// <summary>
        /// GUI更新
        /// </summary>
        void OnGUI();
        
        /// <summary>
        /// 清理状态机资源
        /// </summary>
        void Clear();
    }

    #endregion

    #region 实现类

    /// <summary>
    /// 分层状态基类
    /// </summary>
    /// <typeparam name="T">状态ID类型</typeparam>
    public class HState<T> : IHState
    {
        private Action mOnEnter;
        private Action mOnUpdate;
        private Action mOnFixedUpdate;
        private Action mOnGUI;
        private Action mOnExit;
        private Func<bool> mOnCondition;
        private HFSM<T> mSubStateMachine;

        public IHState Parent { get; set; }
        public IHFSM SubStateMachine => mSubStateMachine;
        public bool HasSubStateMachine => mSubStateMachine != null;

        public HState<T> OnEnter(Action onEnter)
        {
            mOnEnter = onEnter;
            return this;
        }

        public HState<T> OnUpdate(Action onUpdate)
        {
            mOnUpdate = onUpdate;
            return this;
        }

        public HState<T> OnFixedUpdate(Action onFixedUpdate)
        {
            mOnFixedUpdate = onFixedUpdate;
            return this;
        }

        public HState<T> OnGUI(Action onGUI)
        {
            mOnGUI = onGUI;
            return this;
        }

        public HState<T> OnExit(Action onExit)
        {
            mOnExit = onExit;
            return this;
        }

        public HState<T> OnCondition(Func<bool> onCondition)
        {
            mOnCondition = onCondition;
            return this;
        }

        /// <summary>
        /// 创建子状态机
        /// </summary>
        public HFSM<T> CreateSubStateMachine()
        {
            mSubStateMachine = new HFSM<T>();
            return mSubStateMachine;
        }

        #region IState实现

        public bool Condition()
        {
            var result = mOnCondition?.Invoke();
            return result == null || result.Value;
        }

        public void Enter()
        {
            mOnEnter?.Invoke();
            mSubStateMachine?.Start();
        }

        public void Update()
        {
            mOnUpdate?.Invoke();
            mSubStateMachine?.Update();
        }

        public void FixedUpdate()
        {
            mOnFixedUpdate?.Invoke();
            mSubStateMachine?.FixedUpdate();
        }

        public void OnGUI()
        {
            mOnGUI?.Invoke();
            mSubStateMachine?.OnGUI();
        }

        public void Exit()
        {
            mSubStateMachine?.Clear();
            mOnExit?.Invoke();
        }

        #endregion
    }

    /// <summary>
    /// 分层有限状态机
    /// </summary>
    /// <typeparam name="T">状态ID类型</typeparam>
    public class HFSM<T> : IHFSM
    {
        protected Dictionary<T, IHState> mStates = new Dictionary<T, IHState>();
        protected IHState mCurrentState;
        protected T mCurrentStateId;
        protected T mPreviousStateId;
        
        public long FrameCountOfCurrentState = 1;
        public float SecondsOfCurrentState = 0.0f;
        
        public IHState CurrentState => mCurrentState;
        public T CurrentStateId => mCurrentStateId;
        public T PreviousStateId => mPreviousStateId;
        
        private Action<T, T> mOnStateChanged = (_, __) => { };
        
        /// <summary>
        /// 添加状态
        /// </summary>
        public void AddState(T id, IHState state)
        {
            mStates.Add(id, state);
        }
        
        /// <summary>
        /// 获取或创建状态
        /// </summary>
        public HState<T> State(T t)
        {
            if (mStates.TryGetValue(t, out var state))
            {
                return state as HState<T>;
            }

            var newState = new HState<T>();
            mStates.Add(t, newState);
            return newState;
        }

        /// <summary>
        /// 改变当前状态
        /// </summary>
        public void ChangeState(T t)
        {
            if (t.Equals(mCurrentStateId)) return;
            
            if (mStates.TryGetValue(t, out var state))
            {
                if (mCurrentState != null && state.Condition())
                {
                    mCurrentState.Exit();
                    mPreviousStateId = mCurrentStateId;
                    mCurrentState = state;
                    mCurrentStateId = t;
                    mOnStateChanged?.Invoke(mPreviousStateId, mCurrentStateId);
                    FrameCountOfCurrentState = 1;
                    SecondsOfCurrentState = 0.0f;
                    mCurrentState.Enter();
                }
            }
        }

        /// <summary>
        /// 注册状态变化回调
        /// </summary>
        public void OnStateChanged(Action<T, T> onStateChanged)
        {
            mOnStateChanged += onStateChanged;
        }

        /// <summary>
        /// 获取状态的层级路径
        /// </summary>
        public string GetStatePath(IHState state)
        {
            if (state == null) return string.Empty;
            
            var path = string.Empty;
            var current = state;
            
            while (current != null)
            {
                // 在实际项目中可以实现将状态ID转换为字符串的逻辑
                path = "/" + GetStateNameById(current) + path;
                current = current.Parent;
            }
            
            return path;
        }
        
        // 辅助方法，获取状态名称
        private string GetStateNameById(IHState state)
        {
            foreach (var pair in mStates)
            {
                if (pair.Value == state)
                {
                    return pair.Key.ToString();
                }
            }
            return "Unknown";
        }

        #region IHFSM实现

        public void Start()
        {
            if (mCurrentState != null)
            {
                mCurrentState.Enter();
            }
        }

        /// <summary>
        /// 启动指定状态
        /// </summary>
        public void StartState(T t)
        {
            if (mStates.TryGetValue(t, out var state))
            {
                mPreviousStateId = t;
                mCurrentState = state;
                mCurrentStateId = t;
                FrameCountOfCurrentState = 0;
                SecondsOfCurrentState = 0.0f;
                state.Enter();
            }
        }
        
        public void Update()
        {
            mCurrentState?.Update();
            FrameCountOfCurrentState++;
            SecondsOfCurrentState += Time.deltaTime;
        }

        public void FixedUpdate()
        {
            mCurrentState?.FixedUpdate();
        }

        public void OnGUI()
        {
            mCurrentState?.OnGUI();
        }

        public void Clear()
        {
            if (mCurrentState != null)
            {
                mCurrentState.Exit();
            }
            
            mCurrentState = null;
            mCurrentStateId = default;
            mStates.Clear();
        }

        #endregion
    }

    /// <summary>
    /// 分层状态抽象基类
    /// </summary>
    public abstract class AbstractHState<TStateId, TTarget> : IHState
    {
        protected HFSM<TStateId> mFSM;
        protected TTarget mTarget;
        protected HFSM<TStateId> mSubStateMachine;
        
        public IHState Parent { get; set; }
        public IHFSM SubStateMachine => mSubStateMachine;
        public bool HasSubStateMachine => mSubStateMachine != null;

        protected AbstractHState(HFSM<TStateId> fsm, TTarget target)
        {
            mFSM = fsm;
            mTarget = target;
        }
        
        /// <summary>
        /// 创建子状态机
        /// </summary>
        protected HFSM<TStateId> CreateSubStateMachine()
        {
            mSubStateMachine = new HFSM<TStateId>();
            return mSubStateMachine;
        }

        #region IState实现
        
        bool IState.Condition()
        {
            return OnCondition();
        }

        void IState.Enter()
        {
            OnEnter();
            mSubStateMachine?.Start();
        }

        void IState.Update()
        {
            OnUpdate();
            mSubStateMachine?.Update();
        }

        void IState.FixedUpdate()
        {
            OnFixedUpdate();
            mSubStateMachine?.FixedUpdate();
        }

        public virtual void OnGUI()
        {
            mSubStateMachine?.OnGUI();
        }

        void IState.Exit()
        {
            mSubStateMachine?.Clear();
            OnExit();
        }
        
        protected virtual bool OnCondition() => true;

        protected virtual void OnEnter() { }

        protected virtual void OnUpdate() { }

        protected virtual void OnFixedUpdate() { }

        protected virtual void OnExit() { }
        
        #endregion
    }
    
    #endregion
} 