using System;
using UnityEngine;
using QHLC;
using QHLC.Views;
using QHLC.FSM;

namespace QHLC.Events
{
    /// <summary>
    /// 游戏事件定义，用于QFramework的TypeEventSystem
    /// 所有事件都是类，符合QFramework的TypeEventSystem要求
    /// 事件分为四类：框架事件、游戏事件、灯光事件、转盘事件、潜艇事件
    /// </summary>

    //=====================================================================
    // 框架事件 - 与游戏框架相关的事件
    //=====================================================================

    /// <summary>
    /// 主界面初始化游戏事件
    /// 在游戏初始化时触发，用于初始化游戏相关组件
    /// </summary>
    public class MainUIOnInitGameEvent { }

    /// <summary>
    /// 主界面进入游戏事件
    /// 在玩家进入游戏时触发，用于准备游戏场景
    /// </summary>
    public class MainUIOnEnterGameEvent { }

    //=====================================================================
    // 游戏事件 - 与游戏流程相关的事件
    //=====================================================================

    /// <summary>
    /// 游戏开始事件
    /// 在游戏正式开始时触发，用于启动游戏逻辑
    /// </summary>
    public class GameStartedEvent { }

    /// <summary>
    /// 游戏重新开始事件
    /// 在游戏需要重新开始时触发，用于重置游戏状态
    /// </summary>
    public class GameRestartEvent { }

    /// <summary>
    /// 游戏完成事件
    /// 在游戏结束时触发，用于处理游戏结束逻辑
    /// </summary>
    public class GameCompletedEvent { }

    /// <summary>
    /// 游戏状态切换请求事件
    /// 当子系统需要请求GameFSM切换状态时触发，包含请求的目标状态
    /// </summary>
    public class GameStateChangeRequestEvent
    {
        /// <summary>
        /// 请求的目标游戏状态
        /// </summary>
        public GameStateType RequestedState;
    }

    //=====================================================================
    // 灯光事件 - 与灯光模块相关的事件
    //=====================================================================

    /// <summary>
    /// 灯光激活事件
    /// 当单个灯光被激活时触发，包含被激活的灯光索引
    /// </summary>
    public class LightActivatedEvent
    {
        /// <summary>
        /// 被激活的灯光索引，-1表示按顺序激活下一个灯光
        /// </summary>
        public int LightIndex;
    }

    /// <summary>
    /// 所有灯光激活事件
    /// 当所有灯光都被激活时触发，用于触发转盘缓存和灯光动画
    /// </summary>
    public class AllLightsActivatedEvent { }

    /// <summary>
    /// 灯光动画完成事件
    /// 当灯光全亮动画播放完成时触发，用于触发状态切换
    /// </summary>
    public class LightAnimationCompletedEvent { }

    //=====================================================================
    // 转盘事件 - 与转盘模块相关的事件
    //=====================================================================

    /// <summary>
    /// 开始转盘旋转事件
    /// 当需要开始转盘旋转时触发，包含目标结果
    /// </summary>
    public class StartWheelSpinningEvent
    {
        /// <summary>
        /// 目标结果，决定转盘停止的位置
        /// </summary>
        public WheelResult TargetResult;
    }

    /// <summary>
    /// 转盘开始旋转事件
    /// 与StartWheelSpinningEvent功能相同，为了兼容现有代码而保留
    /// </summary>
    public class WheelSpinningStartEvent
    {
        /// <summary>
        /// 目标结果，决定转盘停止的位置
        /// </summary>
        public WheelResult TargetResult;
    }

    /// <summary>
    /// 转盘停止事件
    /// 当转盘停止旋转时触发，包含最终结果
    /// </summary>
    public class WheelStoppedEvent
    {
        /// <summary>
        /// 转盘停止的最终结果
        /// </summary>
        public WheelResult Result;
    }

    /// <summary>
    /// 转盘闪烁完成事件
    /// 当转盘闪烁动画完成时触发，用于触发状态切换
    /// </summary>
    public class WheelFlashingCompletedEvent
    {
        /// <summary>
        /// 转盘闪烁的最终结果
        /// </summary>
        public WheelResult Result;
    }

    /// <summary>
    /// 转盘闪烁开始事件
    /// </summary>
    public class WheelFlashingStartEvent
    {
        public WheelResult Result;
    }

    /// <summary>
    /// 转盘重置事件
    /// 当需要重置转盘状态时触发
    /// </summary>
    public class WheelResetEvent { }

    /// <summary>
    /// 转盘缓存次数变化事件
    /// 当转盘缓存次数发生变化时触发，包含新的缓存次数
    /// </summary>
    public class WheelBufferCountChangedEvent
    {
        /// <summary>
        /// 新的转盘缓存次数
        /// </summary>
        public int Count;
    }

    /// <summary>
    /// 输入记录事件
    /// 当玩家输入被记录到缓存时触发，包含输入事件信息
    /// </summary>
    public class InputRecordedEvent
    {
        /// <summary>
        /// 输入事件信息
        /// </summary>
        public InputEvent Event;
    }

    // 已在上面定义了WheelSpinningStartEvent，这里删除重复定义

    //=====================================================================
    // 潜艇事件 - 与潜艇模块相关的事件
    //=====================================================================

    /// <summary>
    /// 开始潜艇移动事件
    /// 当需要开始潜艇移动时触发，包含移动步数
    /// </summary>
    public class StartSubmarineMovingEvent
    {
        /// <summary>
        /// 移动步数，决定潜艇移动的距离
        /// </summary>
        public int Steps;
    }

    /// <summary>
    /// 潜艇移动开始事件
    /// 当潜艇开始移动时触发，包含详细的移动信息
    /// </summary>
    public class SubmarineMovementStartEvent
    {
        /// <summary>
        /// 当前站点索引，潜艇的起始位置
        /// </summary>
        public int CurrentStationIndex;

        /// <summary>
        /// 目标站点索引，潜艇的目标位置
        /// </summary>
        public int TargetStationIndex;

        /// <summary>
        /// 移动步数，决定潜艇移动的距离
        /// </summary>
        public int Steps;
    }

    /// <summary>
    /// 潜艇移动完成事件
    /// 当潜艇完成所有移动时触发，用于触发状态切换
    /// </summary>
    public class SubmarineMoveCompletedEvent { }

    /// <summary>
    /// 潜艇到达终点事件
    /// 当潜艇到达路径终点时触发
    /// </summary>
    public class SubmarineReachedEndEvent { }

    /// <summary>
    /// 潜艇方向变化事件
    /// 当潜艇移动方向发生变化时触发，包含新的方向
    /// </summary>
    public class SubmarineDirectionChangedEvent
    {
        /// <summary>
        /// 潜艇的新方向
        /// </summary>
        public SubmarineDirection Direction;
    }

    /// <summary>
    /// 潜艇重置事件
    /// 当需要重置潜艇状态时触发
    /// </summary>
    public class SubmarineResetEvent { }
}
