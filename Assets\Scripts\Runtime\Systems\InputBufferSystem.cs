using System.Collections.Generic;
using UnityEngine;
using QFramework;
using F8Framework.Core;
using QHLC.Events;
using QHLC.Models;

namespace QHLC
{
    /// <summary>
    /// 输入缓存系统，负责记录和处理玩家输入
    /// </summary>
    public class InputBufferSystem : AbstractSystem
    {
        // 输入队列，用于存储玩家输入
        private readonly Queue<InputEvent> inputQueue = new Queue<InputEvent>();

        // 最大输入缓存数量
        private const int MaxInputBufferSize = 10;

        protected override void OnInit()
        {
            // 系统初始化
            LogF8.Log("InputBufferSystem: 初始化");

            // 清空输入队列
            inputQueue.Clear();
        }

        /// <summary>
        /// 记录玩家输入
        /// </summary>
        /// <param name="inputEvent">输入事件</param>
        public void RecordInput(InputEvent inputEvent)
        {
            // 验证输入类型
            if (!System.Enum.IsDefined(typeof(InputType), inputEvent.Type))
            {
                LogF8.LogError($"InputBufferSystem: 无效的输入类型 - {inputEvent.Type}");
                return;
            }

            // 验证输入索引（根据输入类型）
            if (inputEvent.Type == InputType.LightActivation && inputEvent.Index >= 0)
            {
                var lightModel = this.GetModel<LightModel>();
                if (inputEvent.Index >= lightModel.TotalLights.Value)
                {
                    LogF8.LogError($"InputBufferSystem: 无效的灯光索引 - {inputEvent.Index}");
                    return;
                }
            }

            // 设置时间戳
            inputEvent.Timestamp = Time.time;

            // 如果队列已满，移除最早的输入
            if (inputQueue.Count >= MaxInputBufferSize)
            {
                inputQueue.Dequeue();
                LogF8.LogWarning("InputBufferSystem: 输入缓存已满，移除最早的输入");
            }

            // 添加新的输入
            inputQueue.Enqueue(inputEvent);
            LogF8.Log($"InputBufferSystem: 记录输入 - {inputEvent.Type}, 索引: {inputEvent.Index}, 时间戳: {inputEvent.Timestamp}");

            // 发送事件通知输入已记录
            TypeEventSystem.Global.Send(new InputRecordedEvent { Event = inputEvent });
        }

        /// <summary>
        /// 获取并处理下一个输入
        /// </summary>
        /// <returns>是否有输入被处理</returns>
        public bool ProcessNextInput()
        {
            if (inputQueue.Count == 0)
            {
                return false;
            }

            // 获取下一个输入
            InputEvent inputEvent = inputQueue.Dequeue();
            LogF8.Log($"InputBufferSystem: 处理输入 - {inputEvent.Type}, 索引: {inputEvent.Index}");

            // 根据输入类型处理
            switch (inputEvent.Type)
            {
                case InputType.LightActivation:
                    // 处理灯光激活输入
                    ProcessLightActivation(inputEvent.Index);
                    break;
                default:
                    LogF8.LogWarning($"InputBufferSystem: 未知的输入类型 - {inputEvent.Type}");
                    break;
            }

            return true;
        }

        /// <summary>
        /// 处理灯光激活输入
        /// </summary>
        /// <param name="lightIndex">灯光索引</param>
        private void ProcessLightActivation(int lightIndex)
        {
            // 获取灯光模型
            var lightModel = this.GetModel<LightModel>();

            // 检查当前游戏状态是否允许激活灯光
            if (lightModel.AllLightsActivated.Value)
            {
                LogF8.LogWarning("InputBufferSystem: 所有灯光已经激活，不允许再激活灯光");
                return;
            }

            // 获取LightView实例
            var lightView = Controllers.GameController.Instance.lightView;
            if (lightView == null)
            {
                LogF8.LogError("InputBufferSystem: 无法获取LightView实例");
                return;
            }

            // 如果索引为-1，表示按空格键激活随机灯光，调用OnSpaceKeyDown方法
            if (lightIndex == -1)
            {
                LogF8.Log("InputBufferSystem: 调用LightView.OnSpaceKeyDown()方法处理空格键输入");
                lightView.OnSpaceKeyDown();
                return;
            }

            // 对于非-1索引，检查索引是否有效
            if (lightIndex < 0 || lightIndex >= lightModel.TotalLights.Value)
            {
                LogF8.LogError($"InputBufferSystem: 无效的灯光索引 - {lightIndex}");
                return;
            }

            // 激活灯光并发送事件
            lightView.ActivateLightAtIndex(lightIndex);
            // 添加这一行，发送事件触发状态切换
            TypeEventSystem.Global.Send(new LightActivatedEvent { LightIndex = lightIndex });
        }

        /// <summary>
        /// 清空输入缓存
        /// </summary>
        public void ClearInputBuffer()
        {
            inputQueue.Clear();
            LogF8.Log("InputBufferSystem: 清空输入缓存");
        }

        /// <summary>
        /// 获取当前缓存的输入数量
        /// </summary>
        /// <returns>缓存的输入数量</returns>
        public int GetBufferedInputCount()
        {
            return inputQueue.Count;
        }

        /// <summary>
        /// 检查是否有缓存的输入
        /// </summary>
        /// <returns>是否有缓存的输入</returns>
        public bool HasBufferedInputs()
        {
            return inputQueue.Count > 0;
        }
    }

    /// <summary>
    /// 输入类型枚举
    /// </summary>
    public enum InputType
    {
        LightActivation,  // 灯光激活
        // 可以添加更多输入类型
    }

    /// <summary>
    /// 输入事件结构
    /// </summary>
    public struct InputEvent
    {
        /// <summary>
        /// 输入类型
        /// </summary>
        public InputType Type;

        /// <summary>
        /// 输入索引（例如，灯光索引）
        /// </summary>
        public int Index;

        /// <summary>
        /// 输入时间戳
        /// </summary>
        public float Timestamp;
    }
}


