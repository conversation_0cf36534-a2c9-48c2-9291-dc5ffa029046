# 更新日志

本文档记录项目的所有重要变更。

## [Unreleased]

- 修复转盘闪烁动画被意外中断导致状态卡死的问题
  - 问题：闪烁动画在播放过程中被意外终止，导致OnComplete回调从未执行，转盘状态无法从Flashing切换到Idle
  - 解决方案：添加闪烁动画超时保护机制，当动画超过预期时间未完成时自动触发完成回调
  - 实现：在WheelView中添加flashTimeoutCoroutine协程，监控动画执行状态
  - 超时时间：闪烁持续时间 + 1秒的缓冲时间
  - 包含完整的协程生命周期管理和错误日志记录
  - 修改日期：2025-01-21

- 增强转盘闪烁动画调试功能
  - 在WheelView.OnWheelFlashingStart中添加详细的调试日志，跟踪闪烁动画创建和播放过程
  - 在WheelUtility.CreateFlashingAnimation中添加调试日志，监控动画序列创建和完成状态
  - 添加对flashImage为null和索引无效情况的错误处理和日志记录
  - 帮助诊断转盘状态没有从Flashing切换到Idle的问题
  - 修改日期：2025-01-21

- 修复WheelSystem中状态切换时序问题导致的路径点规则不一致
  - 移除了WheelSystem.OnFlashingCompleted中的提前状态切换逻辑（GameFSMCoordinator.ChangeState(SubmarineMoving)）
  - 让SubmarineManager自己管理状态切换时序，确保先设置isMoving=true，再切换FSM状态
  - 解决了WheelSystem调用与SubmarineManagerTest调用产生不同路径点规则的问题
  - 确保状态切换时序正确：SubmarineManager.isMoving状态与FSM状态保持同步
  - 修复了PathController状态同步问题，避免了重复的FSM状态切换
  - 修改日期：2025-01-21

- 修复转盘状态切换中的重复调用和状态切换bug
  - 在WheelIdleHState中添加了hasTriggeredSpinning标志位，防止在同一状态周期内重复触发转盘旋转逻辑
  - 优化了WheelBufferSystem中的日志输出，将重复的警告信息改为普通日志，减少日志污染
  - 在GameFSMCoordinator的RequestWheelStateChange方法中添加了状态检查，避免不必要的状态切换请求
  - 修复了WheelIdleHState在OnUpdate中重复检查缓存次数导致的重复调用问题
  - 添加了更详细的状态切换日志，便于调试和问题排查
  - 优化了状态切换的时机控制，确保状态切换的稳定性
  - 修复了WheelFlashingHState等待潜艇移动完成才切换回Idle状态的问题，简化为闪烁完成后直接切换
  - 在GameFSMCoordinator中添加了转盘状态的打印输出，便于调试状态切换问题
  - 移除了转盘状态对潜艇移动状态的依赖，使转盘和潜艇的状态管理更加独立
  - 修复了WheelModel状态同步问题，在各个状态的OnEnter和OnExit方法中正确重置IsSpinning和IsFlashing状态
  - 确保FSM状态切换时WheelModel中的状态标志与FSM状态保持同步，解决了WheelBufferSystem检测状态不一致的问题
  - 修改日期：2025-06-06

- 修复AfterEffectsToUnity导出脚本的兼容性问题
  - 修改了AfterEffectsToUnityCodeConverter.js和AfterEffectsToUnityCodeConverter.jsx文件，添加对Unity 2019+的支持
  - 添加了对TextMeshPro的支持，使导出的代码能够使用TextMeshPro组件
  - 修复了导出的CS文件中父子关系设置错误的问题
  - 创建了AfterEffectsToUnityImplementation.cs文件，提供基本的实现，使导出的CS文件能够正确编译
  - 修改了导出的AfterEffectsToUnitySample.aep.cs文件，使其能够在Unity 2019+中正确编译和运行
  - 修改日期：2025-06-05

- 添加数值策划对接系统重构计划文档
  - 创建了GameValueSystemRefactorPlan.md文档，详细描述了将游戏数据对接给数值策划的系统重构计划
  - 包含了当前情况分析、重构目标、重构计划、具体实现示例和实施时间表
  - 计划分为四个阶段：设计数据结构和配置文件格式、开发配置文件编辑工具、重构现有系统、开发数值调试和分析工具
  - 提供了GameValueConfig和BuildingValueConfig的具体实现示例
  - 修改日期：2025-06-04

- 添加建筑数值控制系统
  - 创建了BuildingValueSystem类，将建筑数据的初始化逻辑从BuildingSystem移到BuildingValueSystem中
  - 添加了建筑数值配置，包括停车场比例、奖励数量范围、金币奖励范围、其他奖励范围和奖励位置偏移范围
  - 将建筑数值配置与游戏数值控制系统集成，使用GameValueSystem管理建筑数值
  - 添加了SubmarinePathInitializedEvent和BuildingsInitializedEvent事件，优化了建筑初始化流程
  - 修改了BuildingSystem类，使其只负责建筑的交互逻辑，而不再负责建筑数据的初始化
  - 修改日期：2025-06-03

- 添加游戏数值控制系统
  - 创建了GameValue类，封装游戏数值及其操作方法
  - 创建了GameValueManager类，管理游戏数值的创建、获取、存储和加载
  - 创建了GameValueSystem类，提供统一的接口来管理游戏数值
  - 创建了GameValueChangedEvent事件，用于通知数值变化
  - 创建了GameValueDebugger工具，用于在游戏运行时查看和修改游戏数值
  - 添加了游戏数值的持久化存储和加载功能
  - 添加了游戏数值的约束功能，如最大值、最小值、步长等
  - 添加了详细的设计文档GameValueControlSystem.md
  - 修改日期：2025-06-02

- 添加DOTween容量初始化设置
  - 在GameInitializer中添加了InitializeDOTween方法
  - 使用DOTween.SetTweensCapacity(500, 100)设置合适的容量
  - 解决了"Max Tweens reached"警告
  - 修改日期：2025-06-01

- 修改BuildingConfig，将normalBuildingSprite改为normalBuildingSprites列表
  - 修改了BuildingConfig.cs，将单个普通建筑精灵改为精灵列表
  - 更新了GetBuildingSprite方法，添加了index参数以支持从列表中获取特定索引的精灵
  - 添加了GetRandomNormalBuildingSprite方法，用于获取随机的普通建筑精灵
  - 修改了BuildingView.cs中的Initialize方法，使用随机的普通建筑精灵
  - 修改了BuildingSystem.cs，添加了对普通建筑精灵列表为空的检查
  - 修改日期：2025-06-01

- 修复RewardIdleAnimation中的DOTween无限循环警告
  - 修改了StartSingleRewardAnimation方法，将无限循环设置在Sequence上而不是在单个Tween上
  - 添加了反向动画，使用LoopType.Restart代替LoopType.Yoyo
  - 解决了"Infinite loops aren't allowed inside a Sequence"的警告
  - 修改日期：2025-05-31

- 修复ConfigurationService中缺少RewardConfig加载的问题
  - 在LoadConfigs方法中添加了RewardConfig的加载逻辑
  - 解决了"无法找到类型为 RewardConfig 的配置"的错误
  - 修改日期：2025-05-31

- 修改BuildingController，使用场景中已有的BuildingView组件而非预设实例化
  - 移除了buildingContainer和buildingPrefab字段
  - 添加了buildingViewsList字段，用于在Inspector中拖拽BuildingView组件
  - 修改了InitializeBuildingViews方法，使用场景中已有的BuildingView组件
  - 添加了InitializeBuildingView方法，用于初始化单个BuildingView组件
  - 修改了OnBuildingCreated方法，使用未使用的BuildingView组件
  - 修改日期：2025-05-30

- 修复BuildingSystem中"没有找到任何站点"的错误
  - 添加了延迟初始化机制，如果在初始化时发现路径点为空，就稍后再尝试初始化
  - 添加了最大重试次数限制，避免无限重试
  - 添加了DG.Tweening命名空间引用，使用DOVirtual.DelayedCall实现延迟
  - 修改日期：2025-05-29

- 修复IconWithNum.cs中的日志错误
  - 添加了F8Framework.Core命名空间引用
  - 将Debug.LogError替换为LogF8.LogError
  - 修改日期：2025-05-28

- 修改RewardView，使用IconWithNum组件替代原有的图像和文本组件
  - 将IconWithNum.cs的命名空间从PPet改为QHLC.Common
  - 修改RewardView.cs，使用IconWithNum组件替代原有的rewardImage和amountText
  - 更新Initialize方法，确保IconWithNum组件存在并正确初始化
  - 保留rewardImage引用，用于动画效果
  - 修改日期：2025-05-27

- 更新BuildingSystemUIDesign.md，添加脚本挂载教程并更新建筑类型描述
  - 添加了详细的脚本挂载教程，包括创建建筑预制体、奖励预制体、设置控制器和配置ScriptableObject
  - 更新了建筑类型描述，将建筑类型从五种改为两种（普通建筑和停车场建筑）
  - 更新了BuildingConfig描述，反映了建筑数据由程序动态生成的变化
  - 修复了文档中的格式问题，为代码块添加了语言标识
  - 修改日期：2025-05-26

- 修复BuildingSystem中缺少命名空间引用的问题
  - 添加了`using QHLC.Enums;`以引入RewardType枚举
  - 添加了`using UnityEngine;`以引入Random和Vector2类型
  - 修改日期：2025-05-25

- 更新配置文件和配置管理器，支持新的建筑系统
  - 修改了BuildingConfig.asset，移除了旧的建筑类型精灵，添加了新的建筑类型精灵
  - 更新了ConfigurationService，添加了BuildingConfig的加载逻辑
  - 更新了ConfigManager，添加了BuildingConfig的支持和GetBuildingConfig方法
  - 修改日期：2025-05-24

- 修改建筑系统，简化建筑类型并实现程序化生成建筑数据
  - 修改了BuildingType枚举，将建筑类型简化为NormalBuilding和ParkingBuilding两种
  - 修改了BuildingConfig，更新了建筑精灵和GetBuildingSprite方法
  - 修改了BuildingSystem的InitializeBuildings方法，实现程序化生成建筑数据
  - 添加了GetRewardName和GetRewardDescription方法，用于生成奖励名称和描述
  - 建筑类型与奖励类型解耦，建筑样式不再与奖励类型相关
  - 每3个站点放置一个停车场建筑，其余为普通建筑
  - 每个建筑随机生成1-3个不同类型的奖励
  - 修改日期：2025-05-23

- 修改奖励系统代码，移除RewardButton相关实现，实现自动收集奖励
  - 移除了RewardView中的rewardButton字段
  - 移除了RewardView中的按钮点击事件和OnClick方法
  - 修改了BuildingController，在建筑交互时自动发送CollectRewardEvent事件
  - 修改日期：2025-05-22

- 更新BuildingSystemUIDesign.md，移除RewardButton相关描述
  - 移除了奖励预制体结构中的RewardButton组件
  - 更新了奖励状态描述，移除了点击交互相关内容
  - 更新了奖励交互流程，改为奖励自动收集
  - 更新了奖励动画描述，移除了点击相关内容
  - 修改日期：2025-05-21

- 更新建筑系统相关文档，添加游戏平衡参数和奖励闲置动画说明
  - 在GameDesignDocument.md中添加了游戏平衡参数部分，详细说明了各种奖励倍率参数
  - 在BuildingSystemUIDesign.md中扩展了奖励动画部分，添加了关于奖励闲置动画的详细说明
  - 修改日期：2025-05-20

- 简化BuildingView组件，移除不必要的UI元素
  - 移除了BuildingView中的interactionUI、buildingNameText和buildingDescriptionText
  - 更新了BuildingController，移除对ShowInteractionUI和HideInteractionUI方法的调用
  - 更新了BuildingSystemUIDesign.md文档，反映了BuildingView的简化设计
  - 修改日期：2025-05-19

- 添加BuildingSystemUIDesign.md，详细说明建筑系统UI设计
  - 创建了完整的建筑系统UI设计文档，包括UI组件结构、建筑UI设计、奖励UI设计等
  - 详细描述了不同类型建筑和奖励的视觉表现
  - 说明了建筑和奖励的状态及其视觉效果
  - 详细描述了奖励交互的流程
  - 说明了各种动画效果的实现方式
  - 提供了UI资源管理和配置的详细说明
  - 提供了UI适配和最佳实践的指导
  - 修改日期：2025-05-18

- 更新GameDesignDocument.md，添加详细的建筑系统设计文档
  - 添加了建筑系统架构的详细说明，包括Model、System、Controller、View、Config和Events
  - 扩展了建筑类型的描述，添加了特点、外观和位置信息
  - 扩展了奖励类型的描述，添加了用途、获取方式和显示信息
  - 添加了建筑数据结构和奖励数据结构的详细说明
  - 添加了建筑配置的详细说明
  - 添加了奖励动画效果的描述
  - 添加了建筑系统与其他系统交互的说明
  - 修改日期：2025-05-17

- 修复ModuleLogManager缺少通用日志方法的问题
  - 添加了通用日志方法：Log、LogWarning和LogError
  - 添加了通用日志开关：EnableGeneralLogs
  - 添加了通用日志开关控制方法：SetGeneralLogsEnabled
  - 更新了ModuleLogController，支持通用日志的开关控制
  - 更新了ProgrammingGuidelines.md，添加了通用日志的使用说明
  - 修改日期：2025-05-16

- 添加建筑系统，实现站点上的建筑和奖励功能
  - 创建了Building和Reward数据类，用于定义建筑和奖励的属性和状态
  - 创建了BuildingModel和RewardModel，管理建筑和奖励数据
  - 创建了BuildingSystem和RewardSystem，处理建筑和奖励的业务逻辑
  - 创建了BuildingController和RewardController，管理建筑和奖励的UI交互
  - 创建了BuildingView和RewardView，负责建筑和奖励的视觉表现
  - 创建了BuildingConfig和RewardConfig，存储建筑和奖励的配置数据
  - 创建了BuildingEvents和RewardEvents，定义建筑和奖励相关的事件
  - 修改了MainUIView，添加了获取UI位置的方法，用于奖励飞向UI的动画
  - 创建了GameDataModel，存储游戏状态和统计数据，包括金币、彩票、扭蛋和JP星星等
  - 修改了QHLCArchitecture，注册了新的Model和System
  - 使用UGUI系统实现所有UI组件，确保与现有UI系统兼容
  - 修改日期：2025-05-15

- 修复WheelUtility.CreateWheelRotationAnimation方法中的旋转方向问题
  - 修改了DOTween旋转动画的设置，使用RotateMode.LocalAxisAdd模式
  - 使用相对旋转值而不是绝对旋转值，确保旋转方向是顺时针的
  - 负值表示顺时针旋转，正值表示逆时针旋转
  - 修改了加速、匀速、减速和回弹阶段的旋转设置
  - 修改日期：2025-05-12

- 修复WheelUtility.CreateWheelRotationAnimation方法中的旋转方向检测逻辑
  - 重构了IsClockwiseRotation方法，使用统一的标准定义判断旋转方向
  - 在Unity中，顺时针旋转表现为角度值减少，逆时针旋转表现为角度值增加
  - 修复了0/360度临界点的特殊处理逻辑
  - 添加了详细的注释，解释旋转方向与角度变化的关系
  - 修改日期：2025-05-11

- 修复WheelUtility.CreateWheelRotationAnimation方法中的旋转方向检测逻辑
  - 修正了IsClockwiseRotation方法，使其与DOTween的旋转方向定义一致
  - 在DOTween动画中，角度值增加实际上表示顺时针旋转，这与传统的角度定义相反
  - 添加了对0/360度临界点的特殊处理，确保正确检测旋转方向
  - 添加了详细的注释，解释旋转方向与角度变化的关系
  - 修改日期：2025-05-10

- 重构WheelUtility.CreateWheelRotationAnimation方法，确保顺时针旋转
  - 添加了IsClockwiseRotation辅助方法，用于检测旋转方向是否为顺时针
  - 在每个旋转阶段的OnUpdate回调中添加了旋转方向检测逻辑
  - 确保totalRotation始终为负值（顺时针旋转）
  - 添加了对各阶段角度的检查和修正，确保严格递减（顺时针旋转）
  - 添加了详细的警告日志，在检测到逆时针旋转时记录相关信息
  - 修改日期：2025-05-09

- 优化转盘旋转动画的顺滑度
  - 重构了WheelUtility.CreateWheelRotationAnimation方法，使用OnComplete回调在匀速阶段真正结束时获取实际角度
  - 将减速阶段的动画创建放在匀速阶段的OnComplete回调中，确保使用最新的角度值
  - 使用InCubic和OutCubic缓动函数替代InQuad和OutQuad，获得更平滑的加速和减速效果
  - 为所有阶段添加OnUpdate回调，记录转盘角度变化，便于调试
  - 添加了更详细的日志输出，记录各阶段的实际角度和旋转情况
  - 修改日期：2025-05-08

- 修复转盘减速阶段不是从0度开始的问题
  - 修改了WheelUtility.CreateWheelRotationAnimation方法，在匀速阶段结束后获取转盘的实际当前角度
  - 使用实际当前角度作为减速阶段的起始点，而不是假设转盘已经精确地旋转到了0度
  - 重新计算从实际当前角度到目标角度的旋转量，确保减速阶段的旋转正确
  - 添加了详细的日志输出，记录匀速阶段结束时的实际角度
  - 修改日期：2025-05-07

- 确保转盘旋转各阶段持续时间符合配置值
  - 修改了WheelUtility.CreateWheelRotationAnimation方法中匀速阶段的持续时间计算
  - 添加了确保匀速阶段持续时间至少等于传入的spinDuration参数值的逻辑
  - 使用Mathf.Max函数确保计算出的持续时间不会小于配置值
  - 添加了详细的日志输出，记录匀速阶段持续时间的计算值和最终值
  - 修改日期：2025-05-06

- 修复转盘旋转方向改变的问题
  - 修改了WheelUtility.CreateWheelRotationAnimation方法的减速阶段，确保从0度到目标角度的旋转始终是顺时针的
  - 添加了计算从0度到目标角度的旋转角度的逻辑，确保角度为负值（顺时针旋转）
  - 使用计算出的最终旋转角度进行旋转，而不是直接旋转到目标角度
  - 修改了日志输出，记录最终旋转角度而非目标角度
  - 修改了总旋转角度的计算，使用最终旋转角度替代目标角度
  - 修改日期：2025-05-05

- 优化转盘旋转动画，让转盘先转到0度位置再进入减速阶段
  - 修改了WheelUtility.CreateWheelRotationAnimation方法的匀速旋转阶段，使其结束角度为0度
  - 重新设计了旋转动画的三个阶段：加速阶段、匀速旋转阶段（到0度）、减速阶段（从0度到目标角度）
  - 确保旋转至少完成指定的额外旋转圈数，同时保证匀速旋转阶段结束时转盘角度为0度
  - 调整了匀速旋转阶段的持续时间，根据旋转角度比例进行调整
  - 添加了详细的日志输出，跟踪旋转角度的计算过程和动画创建过程
  - 修改日期：2025-05-04

- 优化转盘旋转动画，确保从当前角度开始旋转
  - 修改了WheelUtility.CreateWheelRotationAnimation方法，直接使用wheelRectTransform.localRotation作为初始角度
  - 修改了WheelView.OnWheelSpinningStart方法，记录转盘的实际角度和模型中的角度
  - 修改了WheelIdleState.OnStateExit方法，将转盘的实际角度更新到模型中
  - 添加了详细的日志输出，跟踪转盘实际角度和模型中角度的同步过程
  - 确保转盘总是从当前的视觉位置开始旋转，而不是依赖于模型中存储的角度值
  - 修改日期：2025-05-03

- 为WheelIdleState添加转盘闲置状态下的低速旋转功能
  - 使用DOTween实现转盘在闲置状态下的低速旋转效果
  - 添加了StartIdleRotation方法，创建并播放转盘闲置旋转动画
  - 添加了StopIdleRotation方法，在退出闲置状态时停止动画并更新当前角度
  - 使用DOTween.Sequence创建动画序列，实现连续的旋转效果
  - 旋转速度设置为正常旋转速度的约20%（40度/秒）
  - 确保转盘在停下来的位置符合需要出奖的结果
  - 添加了详细的日志输出，跟踪旋转动画的创建、播放和停止过程
  - 修改日期：2025-05-02

- 为SubmarineIdleState添加潜艇闲置动画
  - 使用DOTween实现潜艇在闲置状态下的呼吸效果动画
  - 添加了CreateAndPlayIdleAnimation方法，创建并播放潜艇闲置动画
  - 添加了StopIdleAnimation方法，在退出闲置状态时停止动画并恢复原始缩放
  - 使用DOTween.Sequence创建动画序列，实现缩放和恢复的循环效果
  - 使用SetLoops(-1, LoopType.Restart)设置无限循环
  - 添加了详细的日志输出，跟踪动画的创建、播放和停止过程
  - 修改日期：2025-05-01

- 修复转盘结果为Stop时潜艇不应进入移动状态的问题
  - 修改了SubmarineSystem.MoveSubmarine方法，确保当步数为0时直接触发移动完成事件，不进入移动状态
  - 修改了SubmarineMovingState.OnMoveSubmarineEvent方法，添加对步数为0的处理逻辑
  - 修改了SubmarineMovingState.OnSubmarineMovementStartEvent方法，添加对步数为0的处理逻辑
  - 修改了SubmarineMovingState.OnStateUpdate方法，当步数为0时直接切换到到达状态
  - 添加了详细的日志输出，跟踪Stop结果的处理过程
  - 修改日期：2025-04-29

- 为MoveSteps方法添加结束回调，自动切换潜艇状态
  - 修改了SubmarinePathController.MoveSteps方法，添加了onComplete回调参数
  - 修改了SubmarineView.MoveSteps方法，在移动完成时自动将潜艇状态切回闲置状态
  - 添加了SubmarineFSM组件引用，用于直接控制状态切换
  - 确保即使路径控制器初始化失败也能调用回调，避免状态卡死
  - 添加了详细的日志输出，跟踪移动完成和状态切换过程
  - 修改日期：2025-04-28

- 为FSM类添加Odin可视化功能
  - 为SubmarineFSM、LightFSM和WheelFSM三个FSM类添加了Odin可视化功能
  - 使用ShowInInspector、ReadOnly、BoxGroup等Odin特性，在Unity编辑器中显示FSM状态
  - 添加了当前状态、上一个状态、状态机名称和初始化状态的可视化
  - 修改了OnStateChanged方法，记录上一个状态用于可视化
  - 提高了FSM状态的可调试性和可视化效果
  - 修改日期：2025-04-27

- 为PathItem类的HighlightPathToTarget方法添加详细调试日志
  - 添加了详细的调试日志，记录路径高亮过程中的关键步骤
  - 使用ModuleLogManager.LogSubmarine方法输出日志，确保日志受到EnableSubmarineLogs开关控制
  - 添加了参数验证和错误处理的日志
  - 记录站点查找、路径计算和高亮设置的详细信息
  - 为SetHighlight和SetPassed方法添加了日志记录
  - 修改日期：2025-04-26

- 将HighlightPathToTarget逻辑迁移到PathItem类中
  - 在PathItem类中添加了SetHighlight和SetPassed方法，用于设置路径点的高亮和已通过状态
  - 添加了静态方法HighlightPathToTarget，实现路径高亮逻辑
  - 修改了SubmarineView和SubmarineController中的HighlightPathToTarget方法，使用PathItem中的静态方法
  - 保留了SubmarineController中的闪烁效果逻辑，确保路径点仍然能够闪烁
  - 简化了代码结构，提高了可维护性
  - 修改日期：2025-04-25

- 使用PathKit实现潜艇移动逻辑
  - 修改了PathItem类，使其继承自QFramework.BasePathItem，以便与PathKit集成
  - 创建了SubmarinePathController类，作为PathKit和SubmarineView之间的桥梁
  - 修改了SubmarineView类，使用PathKit的MoveSteps功能替代原有的移动逻辑
  - 创建了专门的PathKitSettings资源，配置潜艇的移动参数和方向精灵
  - 重构了SubmarinePathController，支持从外部传入PathKitSettings
  - 在SubmarineView中添加了从SubmarineConfig加载配置到PathKitSettings的逻辑
  - 弃用了PathItemData类，直接使用PathItem类，简化了代码结构
  - 移除了不再需要的移动逻辑代码
  - 修改日期：2025-04-23

- 完全移除Submarine模块中的旧移动逻辑
  - 删除了SubmarineMovement.cs类
  - 修改了SubmarineView.cs，移除了对SubmarineMovement的引用
  - 修改了SubmarineController.cs，移除了旧的移动协程
  - 确保所有移动逻辑只使用PathKit
  - 修改日期：2025-04-24

- 修复了PathKit模块中循环移动的问题
  - 修改了PathMovementController类中的MoveSteps方法，使其在isLoop为true时也能支持循环移动
  - 确保在路径点到达末尾时能够正确循环回到起点继续移动
  - 修复了潜艇到达路径点24后无法继续移动的问题
  - 修改日期：2025-04-22

- 重构了PathKit模块的架构
  - 将PathManager类重构为更模块化的设计，使用组合而非继承
  - 创建了多个专门的控制器类：PathDataManager、PathMovementController、PathDirectionController、PathEffectController
  - 添加了PathValidator类用于验证路径数据
  - 添加了PathLogger类用于统一日志管理
  - 添加了PathKitConfiguration类用于管理配置
  - 添加了PathObjectPool类用于对象池管理
  - 提高了代码的可维护性和可测试性
  - 修改日期：2025-04-21

- 完善了PathKit模块的功能
  - 添加了PathKitSettings类，用于全局配置路径系统
  - 添加了预设路径功能，支持矩形、圆形、正弦波、之字形、螺旋形等多种路径类型
  - 添加了路径点高亮和特效功能
  - 添加了方向控制功能，支持根据移动方向自动更新精灵
  - 添加了路径编辑器功能，支持在编辑器中可视化路径
  - 添加了路径动画功能，支持多种缓动效果
  - 添加了路径事件系统，支持在路径点和路径完成时触发事件
  - 更新了PathKitExample示例，展示新增功能的使用方法
  - 更新了README.md文档，添加了新功能的使用说明
  - 修改日期：2025-04-20

- 完善了模块日志管理功能
  - 将Wheel、Light和Submarine三个模块中的所有LogF8调用替换为ModuleLogManager对应的方法
  - 包括替换了WheelView、LightView、SubmarineView中的日志调用
  - 包括替换了WheelFSM、LightFSM、SubmarineFSM及相关状态类中的日志调用
  - 包括替换了状态切换类中的日志调用
  - 更新了ProgrammingGuidelines.md文档，添加了模块日志管理的使用规范
  - 修改日期：2025-04-19

- 添加了模块日志管理功能
  - 创建了ModuleLogManager类，用于管理Wheel、Light和Submarine三个模块的日志输出
  - 添加了ModuleLogController组件，提供运行时控制日志输出的功能
  - 修改了相关模块的代码，使用ModuleLogManager替代直接调用LogF8
  - 可以通过F1-F4快捷键或UI界面控制各模块的日志输出
  - 修改日期：2025-04-18

- 修复了潜艇不移动的问题
  - 修改了MainUIView的OnWheelFlashingCompleted方法，直接调用submarineView.MoveSteps方法触发潜艇移动
  - 修改了SubmarineView的MoveSteps方法，确保将IsMoving设置为false，避免移动被阻止
  - 修改了SubmarineMovingState类，移除了OnStateEnter方法中设置潜艇为移动状态的代码
  - 修改了SubmarineMovingState类的事件处理方法，确保正确设置移动标志
  - 添加了更多日志输出，跟踪潜艇移动状态的变化
  - 修改日期：2025-04-18

- 修复了潜艇移动问题
  - 修改了SubmarineMovingState类，在事件处理方法中直接调用MoveSteps方法
  - 修改了SubmarineView的MoveAlongPath方法，使用潜艇当前位置作为路径的第一个点
  - 从 startPathIndex+1 开始添加其他路径点，避免潜艇回到路径点再开始移动
  - 添加了更多日志输出，跟踪潜艇移动路径的计算过程
  - 修改日期：2025-04-17

- 修复了潜艇移动方式不正确的问题
  - 恢复了SubmarineMovingState类的移动逻辑，保留OnStateUpdate中的移动逻辑
  - 在OnStateEnter方法中重置movementStarted标志，确保潜艇能够在Update中开始移动
  - 修改日期：2025-04-17

- 修复了潜艇不移动的问题
  - 修改了SubmarineMovingState类，移除了OnStateEnter方法中设置IsMoving=true的代码
  - 将IsMoving=true的设置移到OnSubmarineMovementStartEvent事件处理方法中
  - 解决了SubmarineMovingState和SubmarineSystem之间的IsMoving标志冲突导致的死锁问题
  - 修改日期：2025-04-16

- 修复了潜艇不移动的问题
  - 修复了SubmarineView的MoveSteps方法，确保正确计算路径点索引并传递给MoveAlongPath方法
  - 重写了SubmarineUtility的CreateDOTweenPath方法，使用DOTween.Sequence和DOTween.To来正确处理RectTransform的路径移动
  - 添加了错误检查和日志输出，以便于调试潜艇移动问题
  - 修改日期：2025-04-16

- 修复了潜艇相关的流程切换问题
  - 移除了SubmarineArrivedState中发送SubmarineMoveCompletedEvent事件的代码，避免重复发送
  - 移除了SubmarineView中重复发送SubmarineMoveCompletedEvent事件的代码
  - 在SubmarineUtility的CreateDOTweenPath方法中添加了OnWaypointChange回调，发送SubmarineStepCompletedEvent事件
  - 修改了SubmarineMovingState，使其监听SubmarineMovementStartEvent事件并正确处理移动步数
  - 确保潜艇状态切换符合游戏设计文档的要求
  - 修改日期：2025-04-15

- 简化了潜艇状态机的实现
  - 删除了潜艇模块的FSMSwitch类（ToSubmarineMovingSwitch、ToSubmarineArrivedSwitch、ToSubmarineIdleSwitch）
  - 修改了SubmarineFSM类，直接使用事件监听器和ChangeState方法切换状态
  - 简化了状态切换逻辑，提高了代码的可维护性
  - 修改日期：2025-04-15

- 优化了转盘缓存和转盘结果获取机制
  - 修改了ToWheelSpinningSwitch类，在转盘开始旋转之前获取转盘结果，但不消耗转盘缓存次数
  - 修改了WheelSystem的OnSpinningCompleted方法，在转盘旋转完成时消耗一次转盘缓存次数
  - 修改了WheelView的OnWheelSpinningStart方法，移除了重复发送WheelFlashingStartEvent事件的代码
  - 确保转盘缓存和转盘结果获取分离，符合游戏设计文档的要求
  - 修改日期：2025-04-15

- 修复了MainUIView中的状态切换问题
  - 修改了MainUIView的OnSubmarineMoveCompletedHandler方法，移除了对已废弃的StartLightFlashing方法的调用
  - 确保潜艇移动完成后的状态切换由FSM自动处理，符合游戏设计文档的要求
  - 修改日期：2025-04-15

- 修复了转盘出结果后潜艇才移动的问题
  - 修改了WheelSystem的OnSpinningCompleted方法，添加了1秒延迟，让用户有时间看到转盘结果
  - 使用DG.Tweening.DOVirtual.DelayedCall实现延迟，延迟后才切换到闪烁状态并发送WheelFlashingStartEvent事件
  - 修改日期：2025-04-15

- 修复了潜艇不从当前位置开始移动的问题
  - 修改了SubmarineView和SubmarineController的MoveAlongPath方法，使用潜艇的当前位置作为路径的第一个点
  - 从 startPathIndex+1 开始添加其他路径点，避免潜艇回到路径点再开始移动
  - 添加了日志输出，跟踪潜艇当前位置和路径计算
  - 修改日期：2025-04-15

- 优化了游戏流程
  - 修改了WheelSystem的OnSpinningCompleted方法，在转盘旋转完成时发送WheelStoppedEvent事件，而不是在闪烁完成后
  - 修改了WheelSystem的OnFlashingCompleted方法，在闪烁完成时发送WheelFlashingCompletedEvent事件
  - 修改了MainUIView，添加对WheelFlashingCompletedEvent事件的监听，在闪烁完成后才移动潜艇
  - 修改日期：2025-04-14

- 修复了潜艇模块的多个问题
  - 修改了SubmarineArrivedState的ProcessArrival方法，移除了消耗转盘缓存次数的代码，保留重置WheelModel的TargetResult属性为Stop的代码
  - 移除了SubmarineArrivedState中重复发送SubmarineMoveCompletedEvent事件的代码，避免事件被重复处理
  - 修改了SubmarineView的MoveAlongPath方法，移除了发送SubmarineMoveCompletedEvent事件的代码，只保留调用SubmarineSystem.OnMovementCompleted方法的代码
  - 修改了SubmarineView的MoveSteps方法，确保正确计算路径点索引，避免移动路径计算错误
  - 修改日期：2025-04-14

- 修复了潜艇不移动的问题
  - 修改了SubmarineMovingState类，使其监听SubmarineMovementStartEvent事件而非MoveSubmarineEvent事件
  - 在SubmarineMovingState的OnStateEnter方法中添加了从转盘模型中获取移动步数的逻辑
  - 添加了日志输出，跟踪潜艇移动事件的接收和处理过程
  - 修改日期：2025-04-14

- 修复了ToWheelSpinningSwitch中缺少随机结果生成的问题
  - 修改了ToWheelSpinningSwitch类的SwitchFunction方法，在消耗转盘次数后生成随机结果并设置到WheelModel中
  - 添加了日志输出，跟踪随机结果的生成过程
  - 修改日期：2025-04-14

- 修复了转盘随机结果生成问题
  - 修改了WheelBufferSystem.DriveWheelSpinning方法，在调用wheelSystem.StartSpinning之前先获取随机结果
  - 修改了WheelFSM.StartSpinning方法，使其不再使用传入的targetResult，而是直接使用WheelModel中的TargetResult属性
  - 修改了WheelSpinningState.OnStateEnter方法，使其不再从前一个状态获取targetResult，而是直接使用WheelModel中的TargetResult属性
  - 添加了更多日志输出，跟踪随机结果的生成和传递过程
  - 修改日期：2025-04-14

- 修复了转盘出奖结果总是Stop的问题
  - 修改了WheelView的OnWheelFlashingStart方法，移除了重复发送WheelStoppedEvent事件的代码
  - 修改了WheelSystem的OnFlashingCompleted方法，添加了日志输出，确保正确使用targetResult
  - 修改了WheelSystem的GetRandomWheelResult方法，当wheelConfig为空时生成[1,5)范围的随机数，避免生成Stop结果
  - 修改了WheelConfig的GetRandomResult方法，过滤掉概率为0的结果，默认返回Move1而非Stop
  - 调整了WheelConfig中的概率设置，降低Stop的概率至0.05，增加其他结果的概率
  - 添加了更多日志输出，以便于调试转盘结果生成过程
  - 修改日期：2025-04-14

- 修复了灯光全亮动画播放完成后没有重置灯光显示状态的问题
  - 修改了LightView的PlayAllLightsActivatedAnimation方法，在动画完成回调中添加了对灯光显示状态的重置
  - 确保灯光全亮动画播放完成后，灯光对象在视图层面被正确重置
  - 避免重复重置灯光状态和缓存转盘次数
  - 符合游戏设计文档中"当三盆灯全部点亮后，缓存一次转盘次数，灯光特效播放完成后回到全灭状态"的要求
  - 修改日期：2025-04-14

- 修复GameFSMCoordinator中LightFSM引用为null的问题
  - 移除了GameController中的lightFSM字段引用，因为现在灯光FSM作为WheelFSM的嵌套状态机
  - 修改了GameFSMCoordinator中的ValidateStateConsistency方法，移除对lightFSM的直接引用
  - 修改了OnFSMStateChanged方法中的日志输出，不再尝试访问不存在的lightFSM
  - 创建了FSMReferenceValidator调试工具，用于验证FSM引用的正确性
  - 修改日期：2025-01-XX

- 添加潜艇移动调试工具和详细日志
  - 创建了SubmarineMovementDebugger组件，提供完整的潜艇移动链路调试功能
  - 在SubmarineView、SubmarineSystem、SubmarineFSM中添加了详细的调试日志
  - 添加了手动触发潜艇移动测试的功能
  - 添加了潜艇移动链路检查功能
  - 添加了强制重置潜艇状态的功能
  - 修改日期：2025-01-XX

- 修复潜艇不移动的问题（第二次修复）
  - 在SubmarinePathController.MoveSteps方法中添加了详细的调试日志和错误处理
  - 添加了pathManager.HasPath检查，如果潜艇未添加到路径会自动重新添加
  - 在AddSubmarineToPath方法中添加了更详细的调试信息，包括PathManager类型检查和异常处理
  - 在SubmarineView中添加了备用移动方法MoveStepsWithDOTween，当PathKit失败时使用DOTween直接移动
  - 修复了编译错误，使用正确的事件结构和系统方法调用
  - 确保即使PathKit初始化失败，潜艇也能通过备用方法移动，避免游戏卡死
  - 修改日期：2025-01-XX

- 修复FSM状态机中的重复调用问题
  - 修复了WheelFlashingHState中的重复状态切换调用问题，添加了hasRequestedStateChange标志位防止重复调用
  - 修复了SubmarineArrivedHState中的重复状态切换调用问题，添加了hasRequestedStateChange标志位防止重复调用
  - 修改了GameFSMCoordinator.HandleWheelFSMStateChanged方法，移除了从转盘旋转到闪烁状态时立即切换到SubmarineMoving的逻辑
  - 修改了GameFSMCoordinator.RequestSubmarineStateChange方法，添加了状态检查，避免重复切换到相同状态
  - 修改了WheelSystem.OnFlashingCompleted方法，在发送MoveSubmarineEvent之前先通过GameFSMCoordinator切换游戏状态到SubmarineMoving
  - 优化了游戏流程：转盘闪烁完成 → 切换游戏状态到SubmarineMoving → 发送MoveSubmarineEvent → 潜艇执行移动
  - 消除了重复的MoveSubmarine方法调用，确保每个状态切换只被调用一次
  - 修改日期：2025-05-28

## 2025-01-XX - Bug修复：重复缓存逻辑问题

### 修复的Bug

#### Bug 1: 转盘系统中的重复消耗缓存次数
- **问题描述**: 在`WheelSystem.OnSpinningCompleted()`方法中，转盘缓存次数被重复消耗
- **原因**: 缓存次数已经在`WheelBufferSystem.DriveWheelSpinning()`中消耗过了，但在旋转完成时又被消耗了一次
- **修复方案**: 删除`WheelSystem.OnSpinningCompleted()`中重复消耗缓存次数的逻辑
- **影响**: 修复后转盘缓存次数将正确管理，不会出现额外消耗的问题

#### Bug 2: 灯光动画完成事件中的重复缓存逻辑
- **问题描述**: 在`LightHFSM.OnLightAnimationCompleted()`方法中，转盘缓存次数被重复添加
- **原因**: 该方法中手动添加了缓存次数，然后又调用了`LightModel.ResetLights(true)`，该方法内部也会添加缓存次数
- **修复方案**: 删除`LightHFSM.OnLightAnimationCompleted()`中手动添加缓存次数的逻辑，只保留`ResetLights(true)`调用
- **影响**: 修复后每次灯光全部激活只会正确添加1次转盘缓存次数，而不是2次

### 验证方法

添加了新的验证方法`ValidateFixedCacheLogic()`到`GameFlowValidator`中，用于验证修复后的缓存逻辑是否正确工作。

### 修改的文件

1. `Assets/Scripts/Runtime/Systems/WheelSystem.cs`
   - 删除了`OnSpinningCompleted()`方法中重复消耗缓存次数的代码

2. `Assets/Scripts/Runtime/FSM/Light/HLight/LightHFSM.cs`
   - 删除了`OnLightAnimationCompleted()`方法中重复添加缓存次数的代码

3. `Assets/Scripts/Runtime/FSM/GameFlowValidator.cs`
   - 添加了`ValidateFixedCacheLogic()`验证方法

### 测试建议

运行`GameFlowValidator.ValidateFixedCacheLogic()`方法来验证修复是否成功。预期结果：
- 每次完成3盏灯的激活流程，转盘缓存次数应该只增加1次
- 转盘旋转完成后，缓存次数不应该被额外消耗

---

## 2025-01-XX - Bug修复：潜艇不移动问题

### 修复的Bug

#### Bug: 潜艇收到移动事件但不实际移动
- **问题描述**: 从日志可以看到潜艇收到了移动开始事件，但实际上没有移动
- **原因分析**:
  1. `SubmarineView.MoveSteps` 方法中的 `pathController` 可能初始化失败
  2. `SubmarinePathController` 构造函数中的 PathKit 初始化可能失败
  3. 缺少详细的错误日志，难以定位具体问题
- **修复方案**:
  1. 在 `SubmarineView.MoveSteps` 方法中添加 `pathController` 重新初始化逻辑
  2. 在 `SubmarinePathController` 构造函数中添加详细的参数验证和错误处理
  3. 在 `SubmarineView.Start` 方法中添加详细的初始化日志
  4. 在 `GameFSMCoordinator.SendMoveSubmarineEventDelayed` 方法中添加详细的事件发送日志
  5. 即使 PathController 初始化失败，也确保状态能够正确切换，避免状态卡死
- **影响**: 修复后潜艇应该能够正常移动，即使 PathKit 初始化失败也不会导致状态卡死

### 修改的文件

1. `Assets/Scripts/Runtime/Views/SubmarineView.cs`
   - 在 `MoveSteps` 方法中添加了 `pathController` 重新初始化逻辑
   - 在 `Start` 方法中添加了详细的初始化日志
   - 添加了失败时的状态切换保护逻辑

2. `Assets/Scripts/Runtime/Controllers/SubmarinePathController.cs`
   - 在构造函数中添加了详细的参数验证和错误处理
   - 在 `AddSubmarineToPath` 方法中添加了详细的日志记录

3. `Assets/Scripts/Runtime/FSM/GameFSMCoordinator.cs`
   - 在 `SendMoveSubmarineEventDelayed` 方法中添加了详细的事件发送日志

### 测试建议

1. 运行游戏并观察潜艇移动是否正常
2. 检查日志中是否有 PathController 初始化失败的错误信息
3. 验证即使 PathController 初始化失败，游戏状态也能正确切换

---
