# SubmarineManager 设置指南

## 快速开始

### 1. 编译错误修复
如果遇到编译错误，请确保：
- `SubmarineManager.cs` 已包含 `using QHLC.Controllers;`
- `SubmarineManagerTest.cs` 中的 Header 属性已正确放置

### 2. 基本设置步骤

#### 步骤1：添加测试组件
1. 在场景中创建一个空的GameObject
2. 添加 `QuickSubmarineTest` 组件
3. 运行游戏，按 T 键测试移动，按 Y 键测试重置

#### 步骤2：检查组件设置
1. 确保场景中有 `SubmarineView` 组件
2. 确保场景中有 `PathItem` 组件（路径点）
3. 确保 `SubmarineView` 的 Inspector 中设置了：
   - submarineRectTransform
   - submarineImage
   - directionSprites
   - pathItems

#### 步骤3：自动设置（推荐）
1. 选择 `SubmarineView` 组件
2. 点击 "获取PathItem组件" 按钮
3. 点击 "设置SubmarineManager组件" 按钮
4. 这会自动配置 SubmarineManager

### 3. 手动设置（如果自动设置失败）

#### 在SubmarineManager中手动设置：
```csharp
// 获取SubmarineManager实例
var manager = SubmarineManager.Instance;

// 手动初始化
manager.InitializeSubmarine();
```

### 4. 测试功能

#### 使用QuickSubmarineTest：
- 按 **T** 键：移动潜艇
- 按 **Y** 键：重置潜艇位置
- 按 **H** 键：显示帮助信息

#### 使用SubmarineManagerTest（Odin Inspector）：
- "测试移动潜艇" 按钮
- "重置潜艇位置" 按钮
- "显示潜艇状态" 按钮
- "初始化SubmarineManager" 按钮
- "连续移动测试" 按钮

#### 使用SimpleSubmarineController：
- 按 **空格** 键：移动潜艇
- 按 **R** 键：重置潜艇

### 5. 编程接口

#### 基本移动：
```csharp
using QHLC.Managers;

// 移动1步
SubmarineManager.Instance.MoveSubmarine(1);

// 移动多步并添加回调
SubmarineManager.Instance.MoveSubmarine(3, () => {
    Debug.Log("移动完成！");
});
```

#### 状态检查：
```csharp
// 检查是否正在移动
bool isMoving = SubmarineManager.Instance.IsMoving();

// 获取当前站点
int currentStation = SubmarineManager.Instance.GetCurrentStationIndex();

// 获取路径点数量
int pathCount = SubmarineManager.Instance.GetPathItemCount();
```

#### 重置位置：
```csharp
SubmarineManager.Instance.ResetSubmarinePosition();
```

### 6. 故障排除

#### 问题1：SubmarineManager未找到
**解决方案：**
- 调用 `SubmarineManager.Instance` 会自动创建实例
- 或者手动在场景中添加 SubmarineManager 组件

#### 问题2：路径点为空
**解决方案：**
- 确保场景中有 PathItem 组件
- 使用 SubmarineView 的 "获取PathItem组件" 按钮
- 检查 PathItem 组件的命名和排序

#### 问题3：潜艇无法移动
**解决方案：**
- 检查控制台日志输出
- 确保 `IsMoving()` 返回 false
- 调用 `InitializeSubmarine()` 重新初始化
- 检查 PathKitSettings 是否正确

#### 问题4：PathController初始化失败
**解决方案：**
- 系统会自动使用 DOTween 备用方案
- 检查 PathKitSettings 配置
- 确保路径点数组不为空

### 7. 日志调试

所有操作都会输出详细日志：
```csharp
// 启用潜艇模块日志
ModuleLogManager.LogSubmarine("你的调试信息");
```

查看控制台输出，所有潜艇相关的日志都以 `[潜艇]` 开头。

### 8. 性能优化建议

1. **避免频繁调用**：移动前检查 `IsMoving()` 状态
2. **使用回调**：在移动完成回调中处理后续逻辑
3. **批量移动**：使用 `MoveSubmarine(steps)` 而不是多次调用
4. **状态缓存**：缓存常用的状态信息

### 9. 与其他系统集成

#### 与FSM集成：
```csharp
// SubmarineManager会自动管理FSM状态
// Moving -> Arrived
```

#### 与事件系统集成：
```csharp
// 监听移动完成事件
TypeEventSystem.Global.Register<SubmarineMoveCompletedEvent>(OnMoveCompleted);
```

#### 与QFramework集成：
```csharp
// SubmarineManager实现了IController接口
// 可以使用QFramework的架构功能
```

### 10. 常用代码片段

#### 条件移动：
```csharp
if (SubmarineManager.Instance != null && !SubmarineManager.Instance.IsMoving())
{
    SubmarineManager.Instance.MoveSubmarine(1, () => {
        // 移动完成后的处理
    });
}
```

#### 移动到指定站点：
```csharp
public void MoveToStation(int targetStation)
{
    var manager = SubmarineManager.Instance;
    int currentStation = manager.GetCurrentStationIndex();
    int pathCount = manager.GetPathItemCount();
    
    int steps = (targetStation - currentStation + pathCount) % pathCount;
    if (steps > 0)
    {
        manager.MoveSubmarine(steps);
    }
}
```

#### 循环移动：
```csharp
IEnumerator AutoMove()
{
    while (true)
    {
        if (!SubmarineManager.Instance.IsMoving())
        {
            SubmarineManager.Instance.MoveSubmarine(1);
        }
        yield return new WaitForSeconds(3f);
    }
}
```

通过这个设置指南，您应该能够快速配置和使用新的SubmarineManager系统。 