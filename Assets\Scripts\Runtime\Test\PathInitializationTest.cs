using UnityEngine;
using QFramework;
using QHLC.Events;
using QHLC.Utilities;

namespace QHLC.Test
{
    /// <summary>
    /// 路径点初始化测试脚本
    /// </summary>
    public class PathInitializationTest : MonoBehaviour, IController
    {
        private void Start()
        {
            // 监听路径点初始化事件
            TypeEventSystem.Global.Register<SubmarinePathInitializedEvent>(OnPathInitialized);
            
            ModuleLogManager.Log("PathInitializationTest: 开始监听路径点初始化事件");
        }

        private void OnPathInitialized(SubmarinePathInitializedEvent e)
        {
            ModuleLogManager.Log($"PathInitializationTest: 收到路径点初始化事件，路径点数量: {e.PathItems?.Count ?? 0}");
            
            if (e.PathItems != null && e.PathItems.Count > 0)
            {
                ModuleLogManager.Log("PathInitializationTest: 路径点初始化成功！");
                
                // 列出所有路径点信息
                for (int i = 0; i < e.PathItems.Count; i++)
                {
                    var pathItem = e.PathItems[i];
                    ModuleLogManager.Log($"PathInitializationTest: 路径点 {i} - 站点: {pathItem.isStation}, 名称: {pathItem.stationName}, 索引: {pathItem.stationIndex}");
                }
            }
            else
            {
                ModuleLogManager.LogError("PathInitializationTest: 路径点初始化失败，数据为空");
            }
        }

        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }

        private void OnDestroy()
        {
            TypeEventSystem.Global.UnRegister<SubmarinePathInitializedEvent>(OnPathInitialized);
        }
    }
} 