using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;

namespace QHLC
{
    /// <summary>
    /// 开始转盘闪烁命令
    /// </summary>
    public class StartWheelFlashingCommand : AbstractCommand
    {
        private readonly WheelResult _targetResult;
        
        public StartWheelFlashingCommand(WheelResult targetResult)
        {
            _targetResult = targetResult;
        }
        
        protected override void OnExecute()
        {
            // 调用系统方法开始转盘闪烁
            this.GetSystem<WheelSystem>().StartFlashing(_targetResult);
        }
    }
} 