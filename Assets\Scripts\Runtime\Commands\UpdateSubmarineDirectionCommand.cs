using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;
using QHLC.Views;

namespace QHLC
{
    /// <summary>
    /// 更新潜艇方向命令
    /// </summary>
    public class UpdateSubmarineDirectionCommand : AbstractCommand
    {
        private readonly SubmarineDirection _direction;

        public UpdateSubmarineDirectionCommand(SubmarineDirection direction)
        {
            _direction = direction;
        }

        protected override void OnExecute()
        {
            // 调用系统方法更新方向
            this.GetSystem<SubmarineSystem>().UpdateDirection(_direction);
        }
    }
} 