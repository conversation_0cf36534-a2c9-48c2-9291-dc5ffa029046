using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;
using F8Framework.Core;
using QHLC.Utilities;

namespace QHLC
{
    /// <summary>
    /// 移动潜艇命令
    /// </summary>
    public class MoveSubmarineCommand : AbstractCommand
    {
        private readonly int _steps;

        public MoveSubmarineCommand(int steps)
        {
            _steps = steps;
        }

        protected override void OnExecute()
        {
            // 使用静态方法，简化代码
            QHLC.Managers.SubmarineManager.MoveSubmarineStatic(_steps);
        }
    }
}
