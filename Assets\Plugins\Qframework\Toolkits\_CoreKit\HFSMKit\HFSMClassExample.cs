/****************************************************************************
 * Copyright (c) 2023 - 2024 liangxiegame UNDER MIT License
 * 
 * https://qframework.cn
 * https://github.com/liangxiegame/QFramework
 * https://gitee.com/liangxiegame/QFramework
 ****************************************************************************/

using UnityEngine;

namespace QFramework.Example
{
    /// <summary>
    /// HFSM类模式示例
    /// </summary>
    public class HFSMClassExample : MonoBehaviour
    {
        #region 状态定义

        // 主状态枚举
        public enum MainState
        {
            Patrol,
            Alert,
            Attack
        }

        // 巡逻子状态枚举
        public enum PatrolSubState
        {
            Idle,
            Walking,
            Observe
        }

        // 警戒子状态枚举
        public enum AlertSubState
        {
            Look,
            Search,
            Chase
        }

        #endregion

        // 主状态机
        private HFSM<MainState> mMainFSM = new HFSM<MainState>();

        private void Start()
        {
            // 使用类模式设置状态
            mMainFSM.AddState(MainState.Patrol, new PatrolState(mMainFSM, this));
            mMainFSM.AddState(MainState.Alert, new AlertState(mMainFSM, this));
            mMainFSM.AddState(MainState.Attack, new AttackState(mMainFSM, this));

            // 注册状态变化事件
            mMainFSM.OnStateChanged((prevState, nextState) =>
            {
                Debug.Log($"主状态从 {prevState} 变为 {nextState}");
            });

            // 启动主状态机的初始状态
            mMainFSM.StartState(MainState.Patrol);
        }

        private void Update()
        {
            // 更新主状态机
            mMainFSM.Update();

            // 示例：按键控制状态转换
            if (Input.GetKeyDown(KeyCode.P))
            {
                mMainFSM.ChangeState(MainState.Patrol);
            }
            else if (Input.GetKeyDown(KeyCode.A))
            {
                mMainFSM.ChangeState(MainState.Alert);
            }
            else if (Input.GetKeyDown(KeyCode.T))
            {
                mMainFSM.ChangeState(MainState.Attack);
            }
        }

        private void OnGUI()
        {
            mMainFSM.OnGUI();

            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("当前状态: " + mMainFSM.CurrentStateId);
            
            // 显示当前状态路径
            if (mMainFSM.CurrentState != null)
            {
                GUILayout.Label("状态路径: " + mMainFSM.GetStatePath(mMainFSM.CurrentState as IHState));
            }
            
            GUILayout.Label("按P进入巡逻状态，按A进入警戒状态，按T进入攻击状态");
            GUILayout.EndArea();
        }

        private void OnDestroy()
        {
            // 清理状态机资源
            mMainFSM.Clear();
        }

        #region 主状态类

        /// <summary>
        /// 巡逻状态类
        /// </summary>
        private class PatrolState : AbstractHState<MainState, HFSMClassExample>
        {
            private HFSM<PatrolSubState> mSubFSM;

            public PatrolState(HFSM<MainState> fsm, HFSMClassExample target) : base(fsm, target)
            {
                // 创建子状态机
                mSubFSM = new HFSM<PatrolSubState>();
                
                // 添加子状态
                mSubFSM.AddState(PatrolSubState.Idle, new PatrolIdleState(mSubFSM, target));
                mSubFSM.AddState(PatrolSubState.Walking, new PatrolWalkingState(mSubFSM, target));
                mSubFSM.AddState(PatrolSubState.Observe, new PatrolObserveState(mSubFSM, target));
            }

            protected override void OnEnter()
            {
                Debug.Log("进入巡逻状态");
                
                // 设置子状态机的初始状态
                mSubFSM.StartState(PatrolSubState.Idle);
            }

            protected override void OnExit()
            {
                Debug.Log("离开巡逻状态");
            }

            public override void OnGUI()
            {
                base.OnGUI();
                
                GUILayout.BeginArea(new Rect(320, 10, 300, 200));
                GUILayout.Label("巡逻状态控制面板");
                if (GUILayout.Button("切换到警戒状态"))
                {
                    mFSM.ChangeState(MainState.Alert);
                }
                GUILayout.EndArea();
            }
        }

        /// <summary>
        /// 警戒状态类
        /// </summary>
        private class AlertState : AbstractHState<MainState, HFSMClassExample>
        {
            private HFSM<AlertSubState> mSubFSM;

            public AlertState(HFSM<MainState> fsm, HFSMClassExample target) : base(fsm, target)
            {
                // 创建子状态机
                mSubFSM = new HFSM<AlertSubState>();
                
                // 添加子状态
                mSubFSM.AddState(AlertSubState.Look, new AlertLookState(mSubFSM, target));
                mSubFSM.AddState(AlertSubState.Search, new AlertSearchState(mSubFSM, target));
                mSubFSM.AddState(AlertSubState.Chase, new AlertChaseState(mSubFSM, target));
            }

            protected override void OnEnter()
            {
                Debug.Log("进入警戒状态");
                
                // 设置子状态机的初始状态
                mSubFSM.StartState(AlertSubState.Look);
            }

            protected override void OnExit()
            {
                Debug.Log("离开警戒状态");
            }

            public override void OnGUI()
            {
                base.OnGUI();
                
                GUILayout.BeginArea(new Rect(320, 10, 300, 200));
                GUILayout.Label("警戒状态控制面板");
                if (GUILayout.Button("切换到攻击状态"))
                {
                    mFSM.ChangeState(MainState.Attack);
                }
                if (GUILayout.Button("返回巡逻状态"))
                {
                    mFSM.ChangeState(MainState.Patrol);
                }
                GUILayout.EndArea();
            }
        }

        /// <summary>
        /// 攻击状态类
        /// </summary>
        private class AttackState : AbstractHState<MainState, HFSMClassExample>
        {
            public AttackState(HFSM<MainState> fsm, HFSMClassExample target) : base(fsm, target)
            {
            }

            protected override void OnEnter()
            {
                Debug.Log("进入攻击状态");
            }

            protected override void OnUpdate()
            {
                // 攻击状态的更新逻辑
            }

            protected override void OnExit()
            {
                Debug.Log("离开攻击状态");
            }

            public override void OnGUI()
            {
                GUILayout.BeginArea(new Rect(320, 10, 300, 200));
                GUILayout.Label("攻击状态控制面板");
                if (GUILayout.Button("返回警戒状态"))
                {
                    mFSM.ChangeState(MainState.Alert);
                }
                GUILayout.EndArea();
            }
        }

        #endregion

        #region 巡逻子状态类

        /// <summary>
        /// 巡逻-待机子状态
        /// </summary>
        private class PatrolIdleState : AbstractHState<PatrolSubState, HFSMClassExample>
        {
            public PatrolIdleState(HFSM<PatrolSubState> fsm, HFSMClassExample target) : base(fsm, target)
            {
            }

            protected override void OnEnter()
            {
                Debug.Log("进入巡逻-待机子状态");
            }

            protected override void OnExit()
            {
                Debug.Log("离开巡逻-待机子状态");
            }

            public override void OnGUI()
            {
                GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                GUILayout.Label("当前处于巡逻-待机子状态");
                if (GUILayout.Button("切换到巡逻-行走子状态"))
                {
                    mFSM.ChangeState(PatrolSubState.Walking);
                }
                if (GUILayout.Button("切换到巡逻-观察子状态"))
                {
                    mFSM.ChangeState(PatrolSubState.Observe);
                }
                GUILayout.EndArea();
            }
        }

        /// <summary>
        /// 巡逻-行走子状态
        /// </summary>
        private class PatrolWalkingState : AbstractHState<PatrolSubState, HFSMClassExample>
        {
            public PatrolWalkingState(HFSM<PatrolSubState> fsm, HFSMClassExample target) : base(fsm, target)
            {
            }

            protected override void OnEnter()
            {
                Debug.Log("进入巡逻-行走子状态");
            }

            protected override void OnExit()
            {
                Debug.Log("离开巡逻-行走子状态");
            }

            public override void OnGUI()
            {
                GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                GUILayout.Label("当前处于巡逻-行走子状态");
                if (GUILayout.Button("切换到巡逻-待机子状态"))
                {
                    mFSM.ChangeState(PatrolSubState.Idle);
                }
                if (GUILayout.Button("切换到巡逻-观察子状态"))
                {
                    mFSM.ChangeState(PatrolSubState.Observe);
                }
                GUILayout.EndArea();
            }
        }

        /// <summary>
        /// 巡逻-观察子状态
        /// </summary>
        private class PatrolObserveState : AbstractHState<PatrolSubState, HFSMClassExample>
        {
            public PatrolObserveState(HFSM<PatrolSubState> fsm, HFSMClassExample target) : base(fsm, target)
            {
            }

            protected override void OnEnter()
            {
                Debug.Log("进入巡逻-观察子状态");
            }

            protected override void OnExit()
            {
                Debug.Log("离开巡逻-观察子状态");
            }

            public override void OnGUI()
            {
                GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                GUILayout.Label("当前处于巡逻-观察子状态");
                if (GUILayout.Button("切换到巡逻-待机子状态"))
                {
                    mFSM.ChangeState(PatrolSubState.Idle);
                }
                if (GUILayout.Button("切换到巡逻-行走子状态"))
                {
                    mFSM.ChangeState(PatrolSubState.Walking);
                }
                GUILayout.EndArea();
            }
        }

        #endregion

        #region 警戒子状态类

        /// <summary>
        /// 警戒-查看子状态
        /// </summary>
        private class AlertLookState : AbstractHState<AlertSubState, HFSMClassExample>
        {
            public AlertLookState(HFSM<AlertSubState> fsm, HFSMClassExample target) : base(fsm, target)
            {
            }

            protected override void OnEnter()
            {
                Debug.Log("进入警戒-查看子状态");
            }

            protected override void OnExit()
            {
                Debug.Log("离开警戒-查看子状态");
            }

            public override void OnGUI()
            {
                GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                GUILayout.Label("当前处于警戒-查看子状态");
                if (GUILayout.Button("切换到警戒-搜索子状态"))
                {
                    mFSM.ChangeState(AlertSubState.Search);
                }
                if (GUILayout.Button("切换到警戒-追逐子状态"))
                {
                    mFSM.ChangeState(AlertSubState.Chase);
                }
                GUILayout.EndArea();
            }
        }

        /// <summary>
        /// 警戒-搜索子状态
        /// </summary>
        private class AlertSearchState : AbstractHState<AlertSubState, HFSMClassExample>
        {
            public AlertSearchState(HFSM<AlertSubState> fsm, HFSMClassExample target) : base(fsm, target)
            {
            }

            protected override void OnEnter()
            {
                Debug.Log("进入警戒-搜索子状态");
            }

            protected override void OnExit()
            {
                Debug.Log("离开警戒-搜索子状态");
            }

            public override void OnGUI()
            {
                GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                GUILayout.Label("当前处于警戒-搜索子状态");
                if (GUILayout.Button("切换到警戒-查看子状态"))
                {
                    mFSM.ChangeState(AlertSubState.Look);
                }
                if (GUILayout.Button("切换到警戒-追逐子状态"))
                {
                    mFSM.ChangeState(AlertSubState.Chase);
                }
                GUILayout.EndArea();
            }
        }

        /// <summary>
        /// 警戒-追逐子状态
        /// </summary>
        private class AlertChaseState : AbstractHState<AlertSubState, HFSMClassExample>
        {
            public AlertChaseState(HFSM<AlertSubState> fsm, HFSMClassExample target) : base(fsm, target)
            {
            }

            protected override void OnEnter()
            {
                Debug.Log("进入警戒-追逐子状态");
            }

            protected override void OnExit()
            {
                Debug.Log("离开警戒-追逐子状态");
            }

            public override void OnGUI()
            {
                GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                GUILayout.Label("当前处于警戒-追逐子状态");
                if (GUILayout.Button("切换到警戒-查看子状态"))
                {
                    mFSM.ChangeState(AlertSubState.Look);
                }
                if (GUILayout.Button("切换到警戒-搜索子状态"))
                {
                    mFSM.ChangeState(AlertSubState.Search);
                }
                if (GUILayout.Button("提升为攻击状态"))
                {
                    // 通过mTarget引用访问主状态机并切换状态
                    mTarget.mMainFSM.ChangeState(MainState.Attack);
                }
                GUILayout.EndArea();
            }
        }

        #endregion
    }
} 