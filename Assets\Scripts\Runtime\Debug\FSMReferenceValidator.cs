using UnityEngine;
using QHLC.Controllers;
using QHLC.FSM;
using QHLC.Views;
using Sirenix.OdinInspector;
using QHLC.Utilities;

namespace QHLC.Debug
{
    /// <summary>
    /// FSM引用验证器
    /// 用于检查FSM引用是否正确配置
    /// </summary>
    public class FSMReferenceValidator : MonoBehaviour
    {
        [Header("组件引用")]
        [SerializeField] private GameController gameController;
        [SerializeField] private GameFSMCoordinator gameFSMCoordinator;
        
        [Header("验证结果")]
        [ShowInInspector, ReadOnly] private bool gameControllerValid = false;
        [ShowInInspector, ReadOnly] private bool wheelFSMValid = false;
        [ShowInInspector, ReadOnly] private bool submarineFSMValid = false;
        [ShowInInspector, ReadOnly] private bool lightViewValid = false;
        [ShowInInspector, ReadOnly] private bool wheelViewValid = false;
        [ShowInInspector, ReadOnly] private bool submarineViewValid = false;
        
        private void Start()
        {
            // 自动查找组件
            if (gameController == null)
                gameController = FindObjectOfType<GameController>();
            
            if (gameFSMCoordinator == null)
                gameFSMCoordinator = FindObjectOfType<GameFSMCoordinator>();
            
            // 执行验证
            ValidateReferences();
        }
        
        [Button("验证FSM引用")]
        private void ValidateReferences()
        {
            ModuleLogManager.Log("=== FSM引用验证开始 ===");
            
            // 验证GameController
            gameControllerValid = gameController != null;
            ModuleLogManager.Log($"GameController: {(gameControllerValid ? "存在" : "缺失")}");
            
            if (gameControllerValid)
            {
                // 验证WheelFSM
                wheelFSMValid = gameController.wheelFSM != null;
                ModuleLogManager.Log($"WheelFSM: {(wheelFSMValid ? "存在" : "缺失")}");
                
                // 验证SubmarineFSM
                submarineFSMValid = gameController.submarineFSM != null;
                ModuleLogManager.Log($"SubmarineFSM: {(submarineFSMValid ? "存在" : "缺失")}");
                
                // 验证Views
                lightViewValid = gameController.lightView != null;
                ModuleLogManager.Log($"LightView: {(lightViewValid ? "存在" : "缺失")}");
                
                wheelViewValid = gameController.wheelView != null;
                ModuleLogManager.Log($"WheelView: {(wheelViewValid ? "存在" : "缺失")}");
                
                submarineViewValid = gameController.submarineView != null;
                ModuleLogManager.Log($"SubmarineView: {(submarineViewValid ? "存在" : "缺失")}");
                
                // 检查是否还有lightFSM字段（应该没有）
                var lightFSMField = typeof(GameController).GetField("lightFSM", 
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                if (lightFSMField != null)
                {
                    ModuleLogManager.LogWarning("警告：GameController中仍然存在lightFSM字段，应该已被移除");
                }
                else
                {
                    ModuleLogManager.Log("✓ GameController中已正确移除lightFSM字段");
                }
            }
            
            ModuleLogManager.Log("=== FSM引用验证完成 ===");
        }
        
        [Button("测试潜艇移动链路")]
        private void TestSubmarineMovementChain()
        {
            ModuleLogManager.Log("=== 测试潜艇移动链路 ===");
            
            if (!gameControllerValid)
            {
                ModuleLogManager.LogError("GameController不存在，无法测试");
                return;
            }
            
            // 检查潜艇移动的完整链路
            bool chainValid = true;
            
            // 1. 检查GameFSMCoordinator
            if (gameFSMCoordinator == null)
            {
                ModuleLogManager.LogError("GameFSMCoordinator缺失");
                chainValid = false;
            }
            
            // 2. 检查WheelFSM（负责触发潜艇移动）
            if (!wheelFSMValid)
            {
                ModuleLogManager.LogError("WheelFSM缺失，无法触发潜艇移动");
                chainValid = false;
            }
            
            // 3. 检查SubmarineFSM
            if (!submarineFSMValid)
            {
                ModuleLogManager.LogError("SubmarineFSM缺失，无法处理潜艇移动");
                chainValid = false;
            }
            
            // 4. 检查SubmarineView
            if (!submarineViewValid)
            {
                ModuleLogManager.LogError("SubmarineView缺失，无法执行实际移动");
                chainValid = false;
            }
            
            if (chainValid)
            {
                ModuleLogManager.Log("✓ 潜艇移动链路完整");
                
                // 尝试手动触发移动测试
                var debugger = FindObjectOfType<SubmarineMovementDebugger>();
                if (debugger != null)
                {
                    ModuleLogManager.Log("找到SubmarineMovementDebugger，可以使用它进行详细测试");
                }
                else
                {
                    ModuleLogManager.LogWarning("未找到SubmarineMovementDebugger，建议添加该组件进行详细测试");
                }
            }
            else
            {
                ModuleLogManager.LogError("✗ 潜艇移动链路不完整，需要修复上述问题");
            }
            
            ModuleLogManager.Log("=== 潜艇移动链路测试完成 ===");
        }
    }
} 