using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using DG.Tweening;
using QHLC.Utilities;
using QHLC.ScriptableObjects;

namespace QHLC
{
    /// <summary>
    /// 转盘工具类，提供辅助方法
    /// </summary>
    public class WheelUtility : IUtility
    {
        /// <summary>
        /// 创建转盘旋转动画
        /// </summary>
        public Sequence CreateWheelRotationAnimation(
            RectTransform wheelRectTransform,
            float targetAngle,
            WheelConfig config)
        {
            DOTween.KillAll();
            // 创建旋转序列
            Sequence sequence = DOTween.Sequence();

            // 使用转盘当前的实际旋转角度作为起始角度
            float currentRotationZ = wheelRectTransform.localRotation.eulerAngles.z;
            // 新增：放大圈数，避免0/360度临界问题
            const int enlargeRounds = 5;
            float enlargedCurrent = currentRotationZ + 360f * enlargeRounds;

            wheelRectTransform.localRotation = Quaternion.Euler(0, 0, enlargedCurrent);

            ModuleLogManager.LogWheel($"开始旋转动画 - 实际起始角度: {currentRotationZ}, 目标角度: {targetAngle}");
            // enlargedCurrent 已经加了N圈
            // 计算顺时针旋转所需的负角度
            int randomExtraRotations = Random.Range(0, 3); // 0~2圈
            float totalRotations = config.additionalRotations + randomExtraRotations;
            // 顺时针差值
            float clockwiseDelta = ((enlargedCurrent - (targetAngle + 360f * enlargeRounds) + 360f) % 360f);
            if (clockwiseDelta == 0f && currentRotationZ != targetAngle) clockwiseDelta = 360f;

            // 确保totalRotation为负值（顺时针旋转）
            float totalRotation = -(360f * totalRotations + clockwiseDelta);

            // 检查计算的旋转是否是顺时针的
            if (totalRotation > 0)
            {
                ModuleLogManager.LogWheelWarning($"检测到逆时针旋转计算！totalRotation: {totalRotation}，强制修正为顺时针旋转");
                totalRotation = -Mathf.Abs(totalRotation); // 强制为负值
            }

            float enlargedTarget = enlargedCurrent + totalRotation;

            float randomAcceleration = config.accelerationDuration;
            float randomDeceleration = config.slowDownDuration;

            ModuleLogManager.LogWheel($"计划旋转 - enlargedCurrent: {enlargedCurrent}, enlargedTarget: {enlargedTarget}, totalRotation: {totalRotation}, 加速时长: {randomAcceleration}, 减速时长: {randomDeceleration}");

            // 各阶段角度严格递减
            float accelerationEndAngle = enlargedCurrent + totalRotation * 0.4f;
            float uniformEndAngle = enlargedCurrent + totalRotation * 0.8f;
            float decelerationEndAngle = enlargedTarget;

            // 确保各阶段角度严格递减（顺时针旋转）
            if (accelerationEndAngle > enlargedCurrent || uniformEndAngle > accelerationEndAngle || decelerationEndAngle > uniformEndAngle)
            {
                ModuleLogManager.LogWheelWarning($"检测到阶段角度计算错误！强制修正为顺时针旋转");
                accelerationEndAngle = enlargedCurrent - Mathf.Abs(totalRotation * 0.4f);
                uniformEndAngle = enlargedCurrent - Mathf.Abs(totalRotation * 0.8f);
                decelerationEndAngle = enlargedCurrent - Mathf.Abs(totalRotation);
            }

            ModuleLogManager.LogWheel($"阶段角度: {enlargedCurrent} -> {accelerationEndAngle} -> {uniformEndAngle} -> {decelerationEndAngle}");

            // 用于检测旋转方向的前一帧角度
            float previousFrameAngle = enlargedCurrent;
            // 加速阶段
            // 使用相对旋转值，确保旋转方向是顺时针的
            float accelerationRelativeRotation = -Mathf.Abs(totalRotation * 0.6f); // 负值表示顺时针旋转

            // 使用DOTween.To方法实现加速阶段动画
            float startRotation = enlargedCurrent;
            float endRotation = startRotation + accelerationRelativeRotation; // 目标角度（顺时针旋转，所以是加上负值）

            Tween accelerationTween = DOTween.To(
                () => startRotation, // 起始值获取器
                (x) => {
                    // 设置当前旋转角度
                    wheelRectTransform.localRotation = Quaternion.Euler(0, 0, x);

                    // 获取当前实际角度用于日志和检测
                    float currentAngle = wheelRectTransform.localRotation.eulerAngles.z;
                    //ModuleLogManager.LogWheel($"加速:当前角度: {currentAngle}");

                    // 检测旋转方向
                    if (!IsClockwiseRotation(previousFrameAngle, currentAngle))
                    {
                        ModuleLogManager.LogWheelWarning($"加速阶段检测到逆时针旋转！前一帧: {previousFrameAngle}, 当前帧: {currentAngle}");
                    }

                    previousFrameAngle = currentAngle;
                    startRotation = x; // 更新当前值
                },
                endRotation, // 目标值
                randomAcceleration) // 动画持续时间
                .SetEase(DG.Tweening.Ease.InQuad)
                .OnStart(() => ModuleLogManager.LogWheel($"加速阶段开始: 相对旋转 {accelerationRelativeRotation}"));

            // 匀速阶段
            // 使用相对旋转值，确保旋转方向是顺时针的
            float uniformRelativeRotation = -Mathf.Abs(totalRotation * 0.4f); // 负值表示顺时针旋转
            float uniformWithAdditionalRotation = uniformRelativeRotation - config.additionalRotations * 360f; // 包含额外旋转圈数

            // 使用DOTween.To方法实现匀速阶段动画
            // 从加速阶段结束的位置开始
            float uniformStartRotation = endRotation; // 使用加速阶段结束时的角度作为起始角度
            float uniformEndRotation = uniformStartRotation + uniformWithAdditionalRotation; // 目标角度

            Tween uniformTween = DOTween.To(
                () => uniformStartRotation, // 起始值获取器
                (x) => {
                    // 设置当前旋转角度
                    wheelRectTransform.localRotation = Quaternion.Euler(0, 0, x);

                    // 获取当前实际角度用于日志和检测
                    float currentAngle = wheelRectTransform.localRotation.eulerAngles.z;
                    //ModuleLogManager.LogWheel($"匀速:当前角度: {currentAngle}");

                    // 检测旋转方向
                    if (!IsClockwiseRotation(previousFrameAngle, currentAngle))
                    {
                        ModuleLogManager.LogWheelWarning($"匀速阶段检测到逆时针旋转！前一帧: {previousFrameAngle}, 当前帧: {currentAngle}");
                    }

                    previousFrameAngle = currentAngle;
                    uniformStartRotation = x; // 更新当前值
                },
                uniformEndRotation, // 目标值
                randomAcceleration * 1.2f) // 动画持续时间
                .SetEase(DG.Tweening.Ease.Linear)
                .OnStart(() => ModuleLogManager.LogWheel($"匀速阶段开始: 相对旋转 {uniformWithAdditionalRotation}"));

            // 减速阶段，使用OutSine缓动
            // 从匀速阶段结束的位置开始
            float decelerationStartRotation = uniformEndRotation; // 使用匀速阶段结束时的角度作为起始角度

            // 标准化目标角度到0-360范围，并考虑enlargeRounds
            float normalizedTargetAngle = (targetAngle % 360f + 360f) % 360f;

            // 在目标角度的判定范围内随机选择一个角度作为最终停留角度
            float randomOffset = Random.Range(-config.judgmentAngle, config.judgmentAngle);
            float randomizedTargetAngle = normalizedTargetAngle + randomOffset;

            // 确保随机化后的角度仍在0-360范围内
            randomizedTargetAngle = (randomizedTargetAngle % 360f + 360f) % 360f;

            ModuleLogManager.LogWheel($"随机化目标角度 - 原始目标: {normalizedTargetAngle}, 随机偏移: {randomOffset}, 最终目标: {randomizedTargetAngle}");

            float enlargedTargetAngle = randomizedTargetAngle + 360f * enlargeRounds;

            // 计算从减速阶段起始角度到目标角度的旋转量
            // 确保是顺时针旋转（负值）
            float decelerationRelativeRotation = 0;

            // 计算顺时针方向的最短路径
            if (decelerationStartRotation > enlargedTargetAngle)
            {
                // 如果起始角度大于目标角度，直接计算差值（负值表示顺时针）
                decelerationRelativeRotation = enlargedTargetAngle - decelerationStartRotation;
            }
            else
            {
                // 如果起始角度小于目标角度，需要减去一圈（360度）来确保顺时针旋转
                decelerationRelativeRotation = enlargedTargetAngle - decelerationStartRotation - 360f;
            }

            // 确保是负值（顺时针旋转）
            decelerationRelativeRotation = -Mathf.Abs(decelerationRelativeRotation);

            // 目标角度就是起始角度加上相对旋转值
            float decelerationEndRotation = decelerationStartRotation + decelerationRelativeRotation;

            ModuleLogManager.LogWheel($"减速阶段计算 - 起始角度: {decelerationStartRotation}, 目标角度: {enlargedTargetAngle}, 相对旋转: {decelerationRelativeRotation}");

            Tween decelerationTween = DOTween.To(
                () => decelerationStartRotation, // 起始值获取器
                (x) => {
                    // 设置当前旋转角度
                    wheelRectTransform.localRotation = Quaternion.Euler(0, 0, x);

                    // 获取当前实际角度用于日志和检测
                    float currentAngle = wheelRectTransform.localRotation.eulerAngles.z;
                    //ModuleLogManager.LogWheel($"减速:当前角度: {currentAngle}");

                    // 检测旋转方向
                    if (!IsClockwiseRotation(previousFrameAngle, currentAngle))
                    {
                        ModuleLogManager.LogWheelWarning($"减速阶段检测到逆时针旋转！前一帧: {previousFrameAngle}, 当前帧: {currentAngle}");
                    }

                    previousFrameAngle = currentAngle;
                    decelerationStartRotation = x; // 更新当前值
                },
                decelerationEndRotation, // 目标值
                randomDeceleration) // 动画持续时间
                .SetEase(DG.Tweening.Ease.OutQuad) // OutQuad
                .OnStart(() => ModuleLogManager.LogWheel($"减速阶段开始: 相对旋转 {decelerationRelativeRotation}"));


            sequence.Append(accelerationTween);
            sequence.Append(uniformTween);
            sequence.Append(decelerationTween);
            //sequence.Append(swing1);
            //sequence.Append(swing2);

            sequence.OnComplete(() =>
            {
                // 获取当前实际角度
                float finalAngle = wheelRectTransform.localRotation.eulerAngles.z;

                // 标准化目标角度到0-360范围
                float normalizedTargetAngle = (targetAngle % 360f + 360f) % 360f;

                // 记录最终角度，但不再强制修正，因为我们已经在动画开始前确保了最终角度是正确的
                ModuleLogManager.LogWheel($"旋转完成 - 最终角度: {finalAngle}, 目标角度: {normalizedTargetAngle}");

                // 标准化最终角度到0-360范围
                float normalizedFinalAngle = (finalAngle % 360f + 360f) % 360f;

                // 检查最终角度是否在目标结果的判定范围内
                WheelResult finalResult = config.GetResultFromAngle(normalizedFinalAngle);
                WheelResult targetResult = config.GetResultFromAngle(normalizedTargetAngle);

                if (finalResult != targetResult)
                {
                    ModuleLogManager.LogWheelWarning($"旋转完成后角度不在目标结果的判定范围内！最终角度: {normalizedFinalAngle}, 最终结果: {finalResult}, 目标角度: {normalizedTargetAngle}, 目标结果: {targetResult}");
                }
                else
                {
                    ModuleLogManager.LogWheel($"旋转完成后角度在目标结果的判定范围内 - 最终结果: {finalResult}, 最终角度: {normalizedFinalAngle}");
                }
            });

            return sequence;
        }
        // 自定义缓动函数，使减速看起来更自然
        private float CustomEasing(float time)
        {
            // 添加一些小的随机波动，使动画看起来不那么机械
            float randomFactor = 1f + Mathf.Sin(time * 20f) * 0.03f;
            // 基于OutQuart缓动函数
            return 1f - Mathf.Pow(1f - time, 4f) * randomFactor;
        }
        /// <summary>
        /// 创建闪烁效果动画
        /// </summary>
        public Sequence CreateFlashingAnimation(
            Image flashImage,
            WheelConfig config)
        {
            Sequence sequence = DOTween.Sequence();

            // 确保图片初始状态是不可见的
            flashImage.gameObject.SetActive(true);
            flashImage.color = new Color(flashImage.color.r, flashImage.color.g, flashImage.color.b, 0);

            // 创建闪烁效果
            for (int i = 0; i < config.flashCount; i++)
            {
                // 淡入
                sequence.Append(DOTween.To(() => flashImage.color, x => flashImage.color = x,
                    new Color(flashImage.color.r, flashImage.color.g, flashImage.color.b, 1f),
                    config.flashDuration / (2 * config.flashCount))
                    .SetEase(DG.Tweening.Ease.InOutQuad));

                // 淡出
                sequence.Append(DOTween.To(() => flashImage.color, x => flashImage.color = x,
                    new Color(flashImage.color.r, flashImage.color.g, flashImage.color.b, 0f),
                    config.flashDuration / (2 * config.flashCount))
                    .SetEase(DG.Tweening.Ease.InOutQuad));
            }

            // 完成后隐藏图片
            sequence.OnComplete(() =>
            {
                flashImage.gameObject.SetActive(false);
            });

            return sequence;
        }

        /// <summary>
        /// 获取结果对应的闪烁效果索引
        /// </summary>
        public int GetFlashEffectIndex(WheelResult result)
        {
            return (int)result;
        }

        /// <summary>
        /// 检查从起始角度到目标角度的旋转是否是顺时针方向
        /// 在Unity中，顺时针旋转表现为角度值减少，逆时针旋转表现为角度值增加
        /// </summary>
        /// <param name="startAngle">起始角度</param>
        /// <param name="endAngle">目标角度</param>
        /// <returns>如果是顺时针旋转返回true，否则返回false</returns>
        private bool IsClockwiseRotation(float startAngle, float endAngle)
        {
            // 标准化角度到0-360范围
            startAngle = (startAngle % 360f + 360f) % 360f;
            endAngle = (endAngle % 360f + 360f) % 360f;

            // 添加一个小的容差值，避免浮点数精度问题
            const float tolerance = 0.1f;

            // 处理0/360度临界点的特殊情况
            if (Mathf.Abs(startAngle - endAngle) > 180f)
            {
                // 如果从接近360度跳变到接近0度，这是顺时针旋转
                return startAngle > endAngle;
            }

            // 角度值减少表示顺时针旋转
            return endAngle <= startAngle + tolerance;
        }
    }
}

