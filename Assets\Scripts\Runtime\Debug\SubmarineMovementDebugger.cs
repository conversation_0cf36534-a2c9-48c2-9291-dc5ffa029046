using UnityEngine;
using QFramework;
using QHLC.Models;
using QHLC.Views;
using QHLC.Events;
using QHLC.FSM;
using Sirenix.OdinInspector;
using QHLC.Utilities;

namespace QHLC.Debug
{
    /// <summary>
    /// 潜艇移动调试器
    /// 用于诊断潜艇不移动的问题
    /// </summary>
    public class SubmarineMovementDebugger : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController
    {
        [Header("调试设置")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private bool autoCheckOnUpdate = false;
        
        [Header("组件引用")]
        [SerializeField] private SubmarineView submarineView;
        [SerializeField] private SubmarineFSM submarineFSM;
        
        [Header("调试信息")]
        [ShowInInspector, ReadOnly] private string currentSubmarineState = "未知";
        [ShowInInspector, ReadOnly] private bool isSubmarineMoving = false;
        [ShowInInspector, ReadOnly] private int currentStationIndex = -1;
        [ShowInInspector, ReadOnly] private bool pathControllerInitialized = false;
        [ShowInInspector, ReadOnly] private int pathItemsCount = 0;
        
        private void Start()
        {
            // 自动查找组件
            if (submarineView == null)
                submarineView = FindObjectOfType<SubmarineView>();
            
            if (submarineFSM == null)
                submarineFSM = FindObjectOfType<SubmarineFSM>();
            
            // 注册事件监听
            TypeEventSystem.Global.Register<MoveSubmarineEvent>(OnMoveSubmarineEvent);
            TypeEventSystem.Global.Register<SubmarineMovementStartEvent>(OnSubmarineMovementStartEvent);
            TypeEventSystem.Global.Register<SubmarineMoveCompletedEvent>(OnSubmarineMoveCompletedEvent);
            
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine("SubmarineMovementDebugger: 调试器已启动");
            }
        }
        
        private void Update()
        {
            if (autoCheckOnUpdate)
            {
                UpdateDebugInfo();
            }
        }
        
        private void OnDestroy()
        {
            // 取消事件监听
            TypeEventSystem.Global.UnRegister<MoveSubmarineEvent>(OnMoveSubmarineEvent);
            TypeEventSystem.Global.UnRegister<SubmarineMovementStartEvent>(OnSubmarineMovementStartEvent);
            TypeEventSystem.Global.UnRegister<SubmarineMoveCompletedEvent>(OnSubmarineMoveCompletedEvent);
        }
        
        [Button("更新调试信息")]
        private void UpdateDebugInfo()
        {
            try
            {
                // 获取潜艇状态
                if (submarineFSM != null)
                {
                    currentSubmarineState = submarineFSM.GetCurrentState().ToString();
                }
                
                // 获取模型信息
                var submarineModel = this.GetModel<SubmarineModel>();
                if (submarineModel != null)
                {
                    isSubmarineMoving = submarineModel.IsMoving.Value;
                    currentStationIndex = submarineModel.CurrentStationIndex.Value;
                    pathItemsCount = submarineModel.GetPathItems().Count;
                }
                
                // 检查PathController
                if (submarineView != null)
                {
                    // 通过反射检查pathController字段
                    var pathControllerField = typeof(SubmarineView).GetField("pathController", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (pathControllerField != null)
                    {
                        var pathController = pathControllerField.GetValue(submarineView);
                        pathControllerInitialized = pathController != null;
                    }
                }
            }
            catch (System.Exception ex)
            {
                if (enableDebugLogs)
                {
                    ModuleLogManager.LogSubmarineError($"SubmarineMovementDebugger: 更新调试信息时发生异常: {ex.Message}");
                }
            }
        }
        
        [Button("手动触发潜艇移动测试")]
        private void TestSubmarineMovement()
        {
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine("SubmarineMovementDebugger: 开始手动测试潜艇移动");
            }
            
            // 发送移动事件
            var moveEvent = new MoveSubmarineEvent { Steps = 1 };
            TypeEventSystem.Global.Send(moveEvent);
            
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine("SubmarineMovementDebugger: 已发送MoveSubmarineEvent事件，步数: 1");
            }
        }
        
        [Button("检查潜艇移动链路")]
        private void CheckMovementChain()
        {
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine("=== 潜艇移动链路检查开始 ===");
            }
            
            UpdateDebugInfo();
            
            // 检查组件
            bool submarineViewExists = submarineView != null;
            bool submarineFSMExists = submarineFSM != null;
            
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine($"1. 组件检查:");
                ModuleLogManager.LogSubmarine($"   - SubmarineView: {(submarineViewExists ? "存在" : "缺失")}");
                ModuleLogManager.LogSubmarine($"   - SubmarineFSM: {(submarineFSMExists ? "存在" : "缺失")}");
                ModuleLogManager.LogSubmarine($"   - PathController初始化: {(pathControllerInitialized ? "是" : "否")}");
                
                ModuleLogManager.LogSubmarine($"2. 状态检查:");
                ModuleLogManager.LogSubmarine($"   - 当前FSM状态: {currentSubmarineState}");
                ModuleLogManager.LogSubmarine($"   - 是否正在移动: {isSubmarineMoving}");
                ModuleLogManager.LogSubmarine($"   - 当前站点索引: {currentStationIndex}");
                ModuleLogManager.LogSubmarine($"   - 路径点数量: {pathItemsCount}");
                
                ModuleLogManager.LogSubmarine("=== 潜艇移动链路检查完成 ===");
            }
        }
        
        [Button("强制重置潜艇状态")]
        private void ForceResetSubmarine()
        {
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine("SubmarineMovementDebugger: 强制重置潜艇状态");
            }
            
            // 发送重置命令
            this.SendCommand<ResetSubmarineCommand>();
            
            // 重置FSM状态
            if (submarineFSM != null)
            {
                submarineFSM.ChangeState(SubmarineStateType.Idle);
            }
            
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine("SubmarineMovementDebugger: 潜艇状态重置完成");
            }
        }
        
        // 事件监听方法
        private void OnMoveSubmarineEvent(MoveSubmarineEvent evt)
        {
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine($"SubmarineMovementDebugger: 监听到MoveSubmarineEvent事件，步数: {evt.Steps}");
            }
        }
        
        private void OnSubmarineMovementStartEvent(SubmarineMovementStartEvent evt)
        {
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine($"SubmarineMovementDebugger: 监听到SubmarineMovementStartEvent事件，步数: {evt.Steps}");
            }
        }
        
        private void OnSubmarineMoveCompletedEvent(SubmarineMoveCompletedEvent evt)
        {
            if (enableDebugLogs)
            {
                ModuleLogManager.LogSubmarine("SubmarineMovementDebugger: 监听到SubmarineMoveCompletedEvent事件");
            }
        }
        
        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }
    }
} 