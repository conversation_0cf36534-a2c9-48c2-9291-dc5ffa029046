/****************************************************************************
 * Copyright (c) 2023 - 2024 liangxiegame UNDER MIT License
 * 
 * https://qframework.cn
 * https://github.com/liangxiegame/QFramework
 * https://gitee.com/liangxiegame/QFramework
 ****************************************************************************/

using UnityEngine;

namespace QFramework.Example
{
    /// <summary>
    /// HFSM示例
    /// </summary>
    public class HFSMExample : MonoBehaviour
    {
        #region 状态定义

        // 主状态枚举
        public enum MainState
        {
            Idle,
            Combat,
            Move
        }

        // 移动子状态枚举
        public enum MoveSubState
        {
            Walk,
            Run,
            Jump
        }

        // 战斗子状态枚举
        public enum CombatSubState
        {
            Attack,
            Defense,
            Skill
        }

        #endregion

        // 主状态机
        private HFSM<MainState> mMainFSM = new HFSM<MainState>();

        private void Start()
        {
            // 配置主状态机的状态
            SetupIdleState();
            SetupMoveState();
            SetupCombatState();

            // 注册状态变化事件
            mMainFSM.OnStateChanged((prevState, nextState) =>
            {
                Debug.Log($"主状态从 {prevState} 变为 {nextState}");
            });

            // 启动主状态机
            mMainFSM.StartState(MainState.Idle);
        }

        private void Update()
        {
            // 更新主状态机
            mMainFSM.Update();

            // 示例：按键控制状态转换
            if (Input.GetKeyDown(KeyCode.M))
            {
                mMainFSM.ChangeState(MainState.Move);
            }
            else if (Input.GetKeyDown(KeyCode.C))
            {
                mMainFSM.ChangeState(MainState.Combat);
            }
            else if (Input.GetKeyDown(KeyCode.I))
            {
                mMainFSM.ChangeState(MainState.Idle);
            }
        }

        private void OnGUI()
        {
            mMainFSM.OnGUI();

            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("当前状态: " + mMainFSM.CurrentStateId);

            // 显示当前状态路径
            if (mMainFSM.CurrentState != null)
            {
                GUILayout.Label("状态路径: " + mMainFSM.GetStatePath(mMainFSM.CurrentState as IHState));
            }

            GUILayout.Label("按M进入移动状态，按C进入战斗状态，按I进入待机状态");
            GUILayout.EndArea();
        }

        private void OnDestroy()
        {
            // 清理状态机资源
            mMainFSM.Clear();
        }

        #region 状态设置

        // 设置待机状态
        private void SetupIdleState()
        {
            mMainFSM.State(MainState.Idle)
                .OnEnter(() =>
                {
                    Debug.Log("进入待机状态");
                })
                .OnUpdate(() =>
                {
                    // 待机状态的更新逻辑
                })
                .OnExit(() =>
                {
                    Debug.Log("离开待机状态");
                });
        }

        // 设置移动状态
        private void SetupMoveState()
        {
            var moveState = mMainFSM.State(MainState.Move);

            moveState
                .OnEnter(() =>
                {
                    Debug.Log("进入移动状态");
                })
                .OnExit(() =>
                {
                    Debug.Log("离开移动状态");
                });

            // 创建移动子状态机
            HFSM<MoveSubState> moveSubFSM = new HFSM<MoveSubState>();

            // 配置移动子状态
            moveSubFSM.State(MoveSubState.Walk)
                .OnEnter(() =>
                {
                    Debug.Log("进入行走子状态");
                })
                .OnGUI(() =>
                {
                    GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                    GUILayout.Label("当前处于行走子状态");
                    if (GUILayout.Button("切换到跑步"))
                    {
                        moveSubFSM.ChangeState(MoveSubState.Run);
                    }
                    if (GUILayout.Button("切换到跳跃"))
                    {
                        moveSubFSM.ChangeState(MoveSubState.Jump);
                    }
                    GUILayout.EndArea();
                });

            moveSubFSM.State(MoveSubState.Run)
                .OnEnter(() =>
                {
                    Debug.Log("进入跑步子状态");
                })
                .OnGUI(() =>
                {
                    GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                    GUILayout.Label("当前处于跑步子状态");
                    if (GUILayout.Button("切换到行走"))
                    {
                        moveSubFSM.ChangeState(MoveSubState.Walk);
                    }
                    if (GUILayout.Button("切换到跳跃"))
                    {
                        moveSubFSM.ChangeState(MoveSubState.Jump);
                    }
                    GUILayout.EndArea();
                });

            moveSubFSM.State(MoveSubState.Jump)
                .OnEnter(() =>
                {
                    Debug.Log("进入跳跃子状态");
                })
                .OnGUI(() =>
                {
                    GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                    GUILayout.Label("当前处于跳跃子状态");
                    if (GUILayout.Button("切换到行走"))
                    {
                        moveSubFSM.ChangeState(MoveSubState.Walk);
                    }
                    if (GUILayout.Button("切换到跑步"))
                    {
                        moveSubFSM.ChangeState(MoveSubState.Run);
                    }
                    GUILayout.EndArea();
                });

            // 设置移动初始子状态
            moveSubFSM.StartState(MoveSubState.Walk);
        }

        // 设置战斗状态
        private void SetupCombatState()
        {
            var combatState = mMainFSM.State(MainState.Combat);

            combatState
                .OnEnter(() =>
                {
                    Debug.Log("进入战斗状态");
                })
                .OnExit(() =>
                {
                    Debug.Log("离开战斗状态");
                });

            // 创建战斗子状态机
            HFSM<CombatSubState> combatSubFSM = new HFSM<CombatSubState>();

            // 配置战斗子状态
            combatSubFSM.State(CombatSubState.Attack)
                .OnEnter(() =>
                {
                    Debug.Log("进入攻击子状态");
                })
                .OnGUI(() =>
                {
                    GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                    GUILayout.Label("当前处于攻击子状态");
                    if (GUILayout.Button("切换到防御"))
                    {
                        combatSubFSM.ChangeState(CombatSubState.Defense);
                    }
                    if (GUILayout.Button("切换到技能"))
                    {
                        combatSubFSM.ChangeState(CombatSubState.Skill);
                    }
                    GUILayout.EndArea();
                });

            combatSubFSM.State(CombatSubState.Defense)
                .OnEnter(() =>
                {
                    Debug.Log("进入防御子状态");
                })
                .OnGUI(() =>
                {
                    GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                    GUILayout.Label("当前处于防御子状态");
                    if (GUILayout.Button("切换到攻击"))
                    {
                        combatSubFSM.ChangeState(CombatSubState.Attack);
                    }
                    if (GUILayout.Button("切换到技能"))
                    {
                        combatSubFSM.ChangeState(CombatSubState.Skill);
                    }
                    GUILayout.EndArea();
                });

            combatSubFSM.State(CombatSubState.Skill)
                .OnEnter(() =>
                {
                    Debug.Log("进入技能子状态");
                })
                .OnGUI(() =>
                {
                    GUILayout.BeginArea(new Rect(10, 220, 300, 200));
                    GUILayout.Label("当前处于技能子状态");
                    if (GUILayout.Button("切换到攻击"))
                    {
                        combatSubFSM.ChangeState(CombatSubState.Attack);
                    }
                    if (GUILayout.Button("切换到防御"))
                    {
                        combatSubFSM.ChangeState(CombatSubState.Defense);
                    }
                    GUILayout.EndArea();
                });

            // 设置战斗初始子状态
            combatSubFSM.StartState(CombatSubState.Attack);
        }

        #endregion
    }
} 