using UnityEngine;
using Sirenix.OdinInspector;
using QHLC.Managers;
using QHLC.Utilities;
using QHLC.Events;
using QFramework;

namespace QHLC.Test
{
    /// <summary>
    /// 游戏流程测试脚本
    /// 用于测试完整的游戏流程，包括事件系统和SubmarineManager的集成
    /// </summary>
    public class GameFlowTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private int testSteps = 1;

        [<PERSON><PERSON>("模拟游戏流程移动潜艇", ButtonSizes.Large)]
        public void TestGameFlowMove()
        {
            ModuleLogManager.LogSubmarine($"GameFlowTest.TestGameFlowMove: 模拟游戏流程移动潜艇，步数: {testSteps}");
            
            // 模拟GameFSMCoordinator发送的MoveSubmarineEvent
            var moveEvent = new MoveSubmarineEvent { Steps = testSteps };
            TypeEventSystem.Global.Send(moveEvent);
            
            ModuleLogManager.LogSubmarine($"GameFlowTest: 已发送MoveSubmarineEvent事件，步数: {testSteps}");
        }

        [But<PERSON>("直接调用SubmarineManager", ButtonSizes.Large)]
        public void TestDirectMove()
        {
            ModuleLogManager.LogSubmarine($"GameFlowTest.TestDirectMove: 直接调用SubmarineManager，步数: {testSteps}");
            
            if (SubmarineManager.Instance != null)
            {
                SubmarineManager.Instance.MoveSubmarine(testSteps, () => {
                    ModuleLogManager.LogSubmarine("GameFlowTest: 直接调用移动完成");
                });
            }
            else
            {
                ModuleLogManager.LogSubmarineError("GameFlowTest: SubmarineManager未找到");
            }
        }

        [Button("测试Stop结果(0步)", ButtonSizes.Large)]
        public void TestStopResult()
        {
            ModuleLogManager.LogSubmarine("GameFlowTest.TestStopResult: 测试Stop结果(0步移动)");
            
            // 发送0步移动事件
            var moveEvent = new MoveSubmarineEvent { Steps = 0 };
            TypeEventSystem.Global.Send(moveEvent);
            
            ModuleLogManager.LogSubmarine("GameFlowTest: 已发送0步移动事件");
        }

        [Button("检查系统状态", ButtonSizes.Large)]
        public void CheckSystemStatus()
        {
            ModuleLogManager.LogSubmarine("=== 系统状态检查 ===");
            
            // 检查SubmarineManager
            if (SubmarineManager.Instance != null)
            {
                var manager = SubmarineManager.Instance;
                ModuleLogManager.LogSubmarine($"SubmarineManager状态:");
                ModuleLogManager.LogSubmarine($"- 当前站点: {manager.GetCurrentStationIndex()}");
                ModuleLogManager.LogSubmarine($"- 是否移动中: {manager.IsMoving()}");
                ModuleLogManager.LogSubmarine($"- 路径点数量: {manager.GetPathItemCount()}");
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager未找到");
            }
            
            // 检查SubmarineFSM
            var submarineFSM = FindObjectOfType<QHLC.FSM.SubmarineFSM>();
            if (submarineFSM != null)
            {
                ModuleLogManager.LogSubmarine($"SubmarineFSM状态: {submarineFSM.GetCurrentState()}");
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineFSM未找到");
            }
            
            // 检查GameFSMCoordinator
            var gameFSM = FindObjectOfType<QHLC.FSM.GameFSMCoordinator>();
            if (gameFSM != null)
            {
                ModuleLogManager.LogSubmarine($"GameFSMCoordinator状态: {gameFSM.GetCurrentState()}");
            }
            else
            {
                ModuleLogManager.LogSubmarineError("GameFSMCoordinator未找到");
            }
            
            ModuleLogManager.LogSubmarine("=== 状态检查完成 ===");
        }

        [Button("重置所有系统", ButtonSizes.Large)]
        public void ResetAllSystems()
        {
            ModuleLogManager.LogSubmarine("GameFlowTest.ResetAllSystems: 重置所有系统");
            
            // 重置SubmarineManager
            if (SubmarineManager.Instance != null)
            {
                SubmarineManager.Instance.ResetSubmarinePosition();
                ModuleLogManager.LogSubmarine("SubmarineManager已重置");
            }
            
            // 重置FSM状态
            var submarineFSM = FindObjectOfType<QHLC.FSM.SubmarineFSM>();
            if (submarineFSM != null)
            {
                submarineFSM.ChangeState(QHLC.FSM.SubmarineStateType.Idle);
                ModuleLogManager.LogSubmarine("SubmarineFSM已重置到Idle状态");
            }
            
            ModuleLogManager.LogSubmarine("所有系统重置完成");
        }

        [Button("连续测试游戏流程", ButtonSizes.Large)]
        public void TestContinuousGameFlow()
        {
            ModuleLogManager.LogSubmarine("GameFlowTest.TestContinuousGameFlow: 开始连续测试");
            StartCoroutine(ContinuousGameFlowCoroutine());
        }

        private System.Collections.IEnumerator ContinuousGameFlowCoroutine()
        {
            for (int i = 0; i < 3; i++)
            {
                ModuleLogManager.LogSubmarine($"=== 连续测试第 {i + 1} 轮 ===");
                
                // 发送移动事件
                var moveEvent = new MoveSubmarineEvent { Steps = 1 };
                TypeEventSystem.Global.Send(moveEvent);
                
                // 等待移动完成
                yield return new WaitForSeconds(3f);
                
                // 检查状态
                CheckSystemStatus();
                
                // 等待下一轮
                yield return new WaitForSeconds(2f);
            }
            
            ModuleLogManager.LogSubmarine("=== 连续测试完成 ===");
        }

        private void Start()
        {
            ModuleLogManager.LogSubmarine("GameFlowTest: 游戏流程测试脚本启动");
        }

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(320, 10, 300, 200));
            GUILayout.Label("=== 游戏流程测试 ===");
            
            if (GUILayout.Button("模拟游戏流程移动"))
            {
                TestGameFlowMove();
            }
            
            if (GUILayout.Button("直接调用SubmarineManager"))
            {
                TestDirectMove();
            }
            
            if (GUILayout.Button("测试Stop结果"))
            {
                TestStopResult();
            }
            
            if (GUILayout.Button("检查系统状态"))
            {
                CheckSystemStatus();
            }
            
            if (GUILayout.Button("重置所有系统"))
            {
                ResetAllSystems();
            }
            
            GUILayout.Space(10);
            GUILayout.Label("对比测试：");
            GUILayout.Label("- 模拟游戏流程：通过事件系统");
            GUILayout.Label("- 直接调用：绕过事件系统");
            
            GUILayout.EndArea();
        }
    }
} 