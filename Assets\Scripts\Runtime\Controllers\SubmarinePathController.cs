using QFramework;
using UnityEngine;
using System.Collections.Generic;
using QHLC.Models;
using QHLC.Events;
using F8Framework.Core;
using QHLC.Utilities; // 添加ModuleLogManager的命名空间

namespace QHLC.Controllers
{
    /// <summary>
    /// 潜艇路径控制器，使用PathKit管理潜艇的移动
    /// </summary>
    public class SubmarinePathController
    {
        private GameObject submarineObject;
        private PathKitSettings pathKitSettings;
        private IPathManager pathManager;
        private List<PathItem> pathItems;

        /// <summary>
        /// 构造函数 - 使用外部传入的PathKitSettings
        /// </summary>
        /// <param name="submarineObject">潜艇游戏对象</param>
        /// <param name="pathItems">路径点列表</param>
        /// <param name="pathKitSettings">PathKit设置</param>
        public SubmarinePathController(GameObject submarineObject, List<PathItem> pathItems, PathKitSettings pathKitSettings)
        {
            ModuleLogManager.LogSubmarine("SubmarinePathController: 开始初始化");
            
            this.submarineObject = submarineObject;
            this.pathItems = pathItems;
            this.pathKitSettings = pathKitSettings;

            // 验证参数
            if (submarineObject == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController: submarineObject为null");
                return;
            }
            
            if (pathItems == null || pathItems.Count == 0)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController: pathItems为空或无效");
                return;
            }
            
            if (pathKitSettings == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController: pathKitSettings为null");
                return;
            }

            ModuleLogManager.LogSubmarine($"SubmarinePathController: 参数验证通过，路径点数量: {pathItems.Count}");

            try
            {
                // 初始化PathKit
                ModuleLogManager.LogSubmarine("SubmarinePathController: 初始化PathKit");
                PathKitCore.InitOnLoad();
                
                pathManager = PathKitCore.Interface.GetSystem<IPathManager>();
                if (pathManager == null)
                {
                    ModuleLogManager.LogSubmarineError("SubmarinePathController: 无法获取PathManager");
                    return;
                }
                
                ModuleLogManager.LogSubmarine("SubmarinePathController: PathManager获取成功");

                // 添加潜艇到路径
                AddSubmarineToPath();
                
                ModuleLogManager.LogSubmarine("SubmarinePathController: 初始化完成");
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarinePathController: 初始化失败，异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 构造函数 - 使用方向精灵创建PathKitSettings
        /// </summary>
        /// <param name="submarineObject">潜艇游戏对象</param>
        /// <param name="pathItems">路径点列表</param>
        /// <param name="directionSprites">方向精灵数组</param>
        public SubmarinePathController(GameObject submarineObject, List<PathItem> pathItems, Sprite[] directionSprites)
        {
            this.submarineObject = submarineObject;
            this.pathItems = pathItems;

            // 初始化PathKit
            PathKitCore.InitOnLoad();
            pathManager = PathKitCore.Interface.GetSystem<IPathManager>();

            // 创建PathKitSettings
            CreatePathKitSettings(directionSprites);

            // 添加潜艇到路径
            AddSubmarineToPath();
        }

        /// <summary>
        /// 创建PathKitSettings
        /// </summary>
        private void CreatePathKitSettings(Sprite[] directionSprites)
        {
            // 创建新的设置
            pathKitSettings = ScriptableObject.CreateInstance<PathKitSettings>();

            // 设置基本参数
            pathKitSettings.defaultMoveDuration = 2.0f;
            pathKitSettings.defaultMoveEase = DG.Tweening.Ease.InOutQuad;
            pathKitSettings.isLoop = true; // 潜艇路径是闭环的

            // 设置方向精灵
            if (directionSprites != null && directionSprites.Length >= 4)
            {
                pathKitSettings.directionType = PathKitSettings.DirectionType.BasicDirections;
                pathKitSettings.upDirectionSprite = directionSprites[0]; // 上
                pathKitSettings.downDirectionSprite = directionSprites[1]; // 下
                pathKitSettings.leftDirectionSprite = directionSprites[2]; // 左
                pathKitSettings.rightDirectionSprite = directionSprites[3]; // 右
            }

            // 设置特效参数
            pathKitSettings.enablePathEffect = true;
            pathKitSettings.effectColor = Color.yellow;
            pathKitSettings.effectDuration = 0.5f;
            pathKitSettings.effectSize = new Vector2(30f, 30f);

            // 设置路径点参数
            pathKitSettings.pathPointVisible = true;
            pathKitSettings.pathPointColor = Color.white;
            pathKitSettings.pathPointHighlightColor = Color.yellow;
            pathKitSettings.pathPointHighlightDuration = 0.5f;

            // 设置自动更新方向
            pathKitSettings.autoUpdateDirection = true;
        }

        /// <summary>
        /// 添加潜艇到路径
        /// </summary>
        private void AddSubmarineToPath()
        {
            ModuleLogManager.LogSubmarine("SubmarinePathController.AddSubmarineToPath: 开始添加潜艇到路径");
            
            // 参考UIRectTransformPathExample的验证方式
            if (submarineObject == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController.AddSubmarineToPath: 潜艇对象不能为空!");
                return;
            }

            // 在转换前再次检查路径点，参考示例代码
            if (pathItems == null || pathItems.Count < 2)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController.AddSubmarineToPath: 路径点不足或未分配，无法创建路径!");
                ModuleLogManager.LogSubmarineError($"SubmarinePathController.AddSubmarineToPath: pathItems={pathItems}, pathItems.Count={pathItems?.Count}");
                return;
            }

            ModuleLogManager.LogSubmarine($"SubmarinePathController.AddSubmarineToPath: 验证通过，路径点数量: {pathItems.Count}");

            // 确保PathKit已正确初始化，参考示例代码
            if (pathManager == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController.AddSubmarineToPath: PathManager为空，尝试重新初始化");
                
                try
                {
                    // 重新初始化PathKit
                    PathKitCore.InitOnLoad();
                    pathManager = PathKitCore.Interface.GetSystem<IPathManager>();
                    
                    if (pathManager == null)
                    {
                        ModuleLogManager.LogSubmarineError("SubmarinePathController.AddSubmarineToPath: 重新初始化后PathManager仍为空");
                        return;
                    }
                    
                    ModuleLogManager.LogSubmarine("SubmarinePathController.AddSubmarineToPath: PathManager重新初始化成功");
                }
                catch (System.Exception ex)
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.AddSubmarineToPath: PathKit重新初始化失败: {ex.Message}");
                    return;
                }
            }

            // 如果潜艇已经在路径中，先移除（参考示例代码的处理方式）
            if (pathManager.HasPath(submarineObject))
            {
                ModuleLogManager.LogSubmarine("SubmarinePathController.AddSubmarineToPath: 潜艇已在路径中，先移除");
                pathManager.RemoveFromPath(submarineObject);
            }

            try
            {
                // 参考UIRectTransformPathExample的加载默认精灵逻辑
                EnsurePathKitSettings();

                // 使用PathManager的具体实现，参考示例代码的类型转换
                ModuleLogManager.LogSubmarine($"SubmarinePathController.AddSubmarineToPath: pathManager类型: {pathManager.GetType().Name}");
                
                if (pathManager is PathManager realManager)
                {
                    ModuleLogManager.LogSubmarine("SubmarinePathController.AddSubmarineToPath: PathManager类型转换成功");
                    
                    // 将PathItem列表转换为BasePathItem列表，参考示例代码的转换方式
                    List<BasePathItem> basePathItems = ConvertPathItemsToBasePathItems(pathItems);
                    
                    if (basePathItems.Count < 2)
                    {
                        ModuleLogManager.LogSubmarineError("SubmarinePathController.AddSubmarineToPath: 转换后的有效路径点不足2个!");
                        return;
                    }

                    ModuleLogManager.LogSubmarine($"SubmarinePathController.AddSubmarineToPath: 转换BasePathItem列表完成，数量: {basePathItems.Count}");

                    // 参考示例代码的添加方式
                    ModuleLogManager.LogSubmarine("SubmarinePathController.AddSubmarineToPath: 准备调用AddToPathWithDirection");
                    
                    if (pathKitSettings != null && pathKitSettings.autoUpdateDirection)
                    {
                        realManager.AddToPathWithDirection(
                            submarineObject,
                            basePathItems,
                            true, // 使用方向精灵
                            PathMoveType.Loop, // 循环移动
                            onReachPoint: (index) => {
                                ModuleLogManager.LogSubmarine($"SubmarinePathController: 到达路径点 {index}");
                                OnReachPathPoint(index);
                            },
                            onComplete: () => {
                                ModuleLogManager.LogSubmarine("SubmarinePathController: 路径移动完成");
                                OnPathComplete();
                            }
                        );
                    }
                    else
                    {
                        realManager.AddToPath(
                            submarineObject,
                            basePathItems,
                            PathMoveType.Loop,
                            onReachPoint: (index) => {
                                ModuleLogManager.LogSubmarine($"SubmarinePathController: 到达路径点 {index}");
                                OnReachPathPoint(index);
                            },
                            onComplete: () => {
                                ModuleLogManager.LogSubmarine("SubmarinePathController: 路径移动完成");
                                OnPathComplete();
                            }
                        );
                    }

                    ModuleLogManager.LogSubmarine("SubmarinePathController.AddSubmarineToPath: 路径添加调用成功");

                    // 应用路径设置，参考示例代码
                    ApplyPathSettings();
                    
                    // 最终验证，参考示例代码
                    bool finalCheck = pathManager.HasPath(submarineObject);
                    ModuleLogManager.LogSubmarine($"SubmarinePathController.AddSubmarineToPath: 最终验证，HasPath结果: {finalCheck}");
                }
                else
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.AddSubmarineToPath: PathManager类型转换失败，实际类型: {pathManager?.GetType().Name ?? "null"}");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarinePathController.AddSubmarineToPath: 添加潜艇到路径时发生错误: {ex.Message}");
                ModuleLogManager.LogSubmarineError($"SubmarinePathController.AddSubmarineToPath: 异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 确保PathKitSettings有效，参考UIRectTransformPathExample
        /// </summary>
        private void EnsurePathKitSettings()
        {
            if (pathKitSettings == null)
            {
                ModuleLogManager.LogSubmarineWarning("SubmarinePathController.EnsurePathKitSettings: PathKitSettings为空，创建默认设置");
                pathKitSettings = ScriptableObject.CreateInstance<PathKitSettings>();
                pathKitSettings.ResetToDefaults(); // 确保设置了默认值
            }
        }

        /// <summary>
        /// 将PathItem列表转换为BasePathItem列表，参考UIRectTransformPathExample
        /// </summary>
        private List<BasePathItem> ConvertPathItemsToBasePathItems(List<PathItem> pathItems)
        {
            if (pathItems == null || pathItems.Count == 0)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController.ConvertPathItemsToBasePathItems: 路径点列表为空或未分配!");
                return new List<BasePathItem>();
            }

            List<BasePathItem> basePathItems = new List<BasePathItem>();
            for (int i = 0; i < pathItems.Count; i++)
            {
                var pathItem = pathItems[i];
                if (pathItem == null)
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.ConvertPathItemsToBasePathItems: 路径点 {i} 为空，跳过");
                    continue;
                }
                
                if (pathItem.transform == null)
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.ConvertPathItemsToBasePathItems: 路径点 {i} ({pathItem.name}) 的transform为空，跳过");
                    continue;
                }

                if (!(pathItem.transform is RectTransform))
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.ConvertPathItemsToBasePathItems: 路径点 {i} ({pathItem.name}) 不是RectTransform类型，跳过");
                    continue;
                }

                basePathItems.Add(pathItem);
            }
            
            ModuleLogManager.LogSubmarine($"SubmarinePathController.ConvertPathItemsToBasePathItems: 转换完成，有效路径点数量: {basePathItems.Count}");
            return basePathItems;
        }

        /// <summary>
        /// 应用路径设置，参考UIRectTransformPathExample
        /// </summary>
        private void ApplyPathSettings()
        {
            if (pathKitSettings != null && pathManager != null)
            {
                ModuleLogManager.LogSubmarine("SubmarinePathController.ApplyPathSettings: 准备更新路径设置");
                pathManager.UpdatePathSettings(submarineObject, pathKitSettings);
                ModuleLogManager.LogSubmarine("SubmarinePathController.ApplyPathSettings: 路径设置更新完成");
            }
            else
            {
                ModuleLogManager.LogSubmarineWarning("SubmarinePathController.ApplyPathSettings: pathKitSettings或pathManager为null，跳过路径设置更新");
            }
        }

        /// <summary>
        /// 到达路径点时的处理
        /// </summary>
        private void OnReachPathPoint(int index)
        {
            // 触发到达点事件
            if (index >= 0 && index < pathItems.Count)
            {
                // 获取站点索引
                int stationIndex = pathItems[index].stationIndex;
                if (stationIndex >= 0)
                {
                    // 使用已有的事件类型
                    TypeEventSystem.Global.Send(new SubmarineStationReachedEvent { StationIndex = stationIndex });
                }
            }
        }

        /// <summary>
        /// 路径完成时的处理
        /// </summary>
        private void OnPathComplete()
        {
            // 触发移动完成事件
            TypeEventSystem.Global.Send(new SubmarineMoveCompletedEvent());
        }

        /// <summary>
        /// 移动潜艇指定步数
        /// </summary>
        /// <param name="steps">移动步数</param>
        /// <param name="onComplete">移动完成回调</param>
        public void MoveSteps(int steps, System.Action onComplete = null)
        {
            ModuleLogManager.LogSubmarine($"SubmarinePathController.MoveSteps: 开始移动，步数: {steps}");
            
            // 参数验证
            if (steps <= 0)
            {
                ModuleLogManager.LogSubmarineWarning($"SubmarinePathController.MoveSteps: 无效步数: {steps}");
                onComplete?.Invoke();
                return;
            }

            // 检查pathManager是否存在
            if (pathManager == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController.MoveSteps: pathManager为null，尝试重新初始化");
                
                try
                {
                    // 尝试重新初始化PathKit
                    PathKitCore.InitOnLoad();
                    pathManager = PathKitCore.Interface.GetSystem<IPathManager>();
                    
                    if (pathManager == null)
                    {
                        ModuleLogManager.LogSubmarineError("SubmarinePathController.MoveSteps: 重新初始化后pathManager仍为null");
                        onComplete?.Invoke();
                        return;
                    }
                    
                    ModuleLogManager.LogSubmarine("SubmarinePathController.MoveSteps: PathManager重新初始化成功");
                }
                catch (System.Exception ex)
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.MoveSteps: PathManager重新初始化失败: {ex.Message}");
                    onComplete?.Invoke();
                    return;
                }
            }
            
            // 检查潜艇对象是否存在
            if (submarineObject == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController.MoveSteps: submarineObject为null，无法移动");
                onComplete?.Invoke();
                return;
            }
            
            // 检查潜艇是否已添加到路径
            bool hasPath = false;
            try
            {
                hasPath = pathManager.HasPath(submarineObject);
                ModuleLogManager.LogSubmarine($"SubmarinePathController.MoveSteps: pathManager.HasPath结果: {hasPath}");
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarinePathController.MoveSteps: 检查HasPath时发生异常: {ex.Message}");
                onComplete?.Invoke();
                return;
            }
            
            if (!hasPath)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController.MoveSteps: 潜艇未添加到路径，尝试重新添加");
                
                try
                {
                    // 尝试重新添加潜艇到路径
                    AddSubmarineToPath();
                    
                    // 再次检查
                    hasPath = pathManager.HasPath(submarineObject);
                    ModuleLogManager.LogSubmarine($"SubmarinePathController.MoveSteps: 重新添加后，pathManager.HasPath结果: {hasPath}");
                    
                    if (!hasPath)
                    {
                        ModuleLogManager.LogSubmarineError("SubmarinePathController.MoveSteps: 重新添加失败，无法移动");
                        onComplete?.Invoke();
                        return;
                    }
                }
                catch (System.Exception ex)
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.MoveSteps: 重新添加路径时发生异常: {ex.Message}");
                    onComplete?.Invoke();
                    return;
                }
            }

            ModuleLogManager.LogSubmarine($"SubmarinePathController.MoveSteps: 准备调用pathManager.MoveSteps，步数: {steps}");

            try
            {
                // 创建安全的回调包装器，确保回调一定会被调用
                bool callbackInvoked = false;
                System.Action safeCallback = () => {
                    if (!callbackInvoked)
                    {
                        callbackInvoked = true;
                        ModuleLogManager.LogSubmarine("SubmarinePathController.MoveSteps: PathKit移动完成，调用回调");
                        onComplete?.Invoke();
                    }
                };

                // 设置超时保护（可选，如果PathKit支持超时参数）
                if (submarineObject != null)
                {
                    // 启动超时检查协程（作为备用方案）
                    var coroutineObject = submarineObject.GetComponent<MonoBehaviour>();
                    if (coroutineObject != null)
                    {
                        coroutineObject.StartCoroutine(TimeoutProtection(10f, () => {
                            if (!callbackInvoked)
                            {
                                ModuleLogManager.LogSubmarineWarning("SubmarinePathController.MoveSteps: 移动超时，强制调用回调");
                                safeCallback();
                            }
                        }));
                    }
                }

                // 使用PathKit的MoveSteps功能
                pathManager.MoveSteps(submarineObject, steps, safeCallback);
                
                ModuleLogManager.LogSubmarine("SubmarinePathController.MoveSteps: pathManager.MoveSteps调用成功");
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarinePathController.MoveSteps: pathManager.MoveSteps调用失败，异常: {ex.Message}");
                onComplete?.Invoke(); // 确保即使失败也调用回调
            }
        }

        /// <summary>
        /// 超时保护协程
        /// </summary>
        private System.Collections.IEnumerator TimeoutProtection(float timeoutSeconds, System.Action onTimeout)
        {
            yield return new WaitForSeconds(timeoutSeconds);
            onTimeout?.Invoke();
        }

        /// <summary>
        /// 停止潜艇移动
        /// </summary>
        public void StopMovement()
        {
            if (pathManager.HasPath(submarineObject))
            {
                pathManager.StopPath(submarineObject);
            }
        }

        /// <summary>
        /// 暂停潜艇移动
        /// </summary>
        public void PauseMovement()
        {
            if (pathManager.HasPath(submarineObject))
            {
                pathManager.PausePath(submarineObject);
            }
        }

        /// <summary>
        /// 恢复潜艇移动
        /// </summary>
        public void ResumeMovement()
        {
            if (pathManager.HasPath(submarineObject))
            {
                pathManager.ResumePath(submarineObject);
            }
        }

        /// <summary>
        /// 设置当前位置（用于同步状态）
        /// </summary>
        /// <param name="stationIndex">目标站点索引</param>
        public void SetCurrentPosition(int stationIndex)
        {
            ModuleLogManager.LogSubmarine($"SubmarinePathController.SetCurrentPosition: 尝试同步位置到站点: {stationIndex}");
            
            if (pathManager == null || submarineObject == null)
            {
                ModuleLogManager.LogSubmarineError("SubmarinePathController.SetCurrentPosition: pathManager或submarineObject为null");
                return;
            }

            try
            {
                // 查找对应的路径点索引
                int pathIndex = -1;
                if (pathItems != null && stationIndex >= 0)
                {
                    for (int i = 0; i < pathItems.Count; i++)
                    {
                        if (pathItems[i].stationIndex == stationIndex)
                        {
                            pathIndex = i;
                            break;
                        }
                    }
                }

                if (pathIndex >= 0)
                {
                    ModuleLogManager.LogSubmarine($"SubmarinePathController.SetCurrentPosition: 找到对应路径点索引: {pathIndex}");
                    
                    // 尝试设置PathKit的当前位置
                    // 注意：这个方法可能不存在于PathKit API中，需要根据实际API调整
                    if (pathManager.HasPath(submarineObject))
                    {
                        // 如果PathKit支持位置设置，使用相应的API
                        // pathManager.SetCurrentPathIndex(submarineObject, pathIndex);
                        
                        ModuleLogManager.LogSubmarineWarning("SubmarinePathController.SetCurrentPosition: PathKit可能不支持直接设置位置，需要重新添加路径");
                        
                        // 作为替代方案，重新添加路径并设置起始位置
                        pathManager.RemoveFromPath(submarineObject);
                        AddSubmarineToPath();
                    }
                }
                else
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.SetCurrentPosition: 未找到站点索引 {stationIndex} 对应的路径点");
                }
            }
            catch (System.Exception ex)
            {
                ModuleLogManager.LogSubmarineError($"SubmarinePathController.SetCurrentPosition: 设置位置时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前PathController的内部位置信息（用于调试）
        /// </summary>
        public void LogCurrentState()
        {
            if (pathManager != null && submarineObject != null)
            {
                try
                {
                    bool hasPath = pathManager.HasPath(submarineObject);
                    ModuleLogManager.LogSubmarine($"SubmarinePathController.LogCurrentState: HasPath = {hasPath}");
                    
                    // 如果PathKit API支持获取当前位置信息，在这里添加
                    // int currentIndex = pathManager.GetCurrentPathIndex(submarineObject);
                    // ModuleLogManager.LogSubmarine($"SubmarinePathController.LogCurrentState: CurrentPathIndex = {currentIndex}");
                }
                catch (System.Exception ex)
                {
                    ModuleLogManager.LogSubmarineError($"SubmarinePathController.LogCurrentState: 获取状态时发生异常: {ex.Message}");
                }
            }
        }
    }
}
