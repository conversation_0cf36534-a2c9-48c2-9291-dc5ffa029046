# HFSM测试框架使用指南

## 概述

本测试框架提供了完整的HFSM（分层有限状态机）测试解决方案，包含自动化测试和手动测试功能，用于验证整个状态机系统的正确性。

## 文件结构

```
Assets/Scripts/Runtime/FSM/
├── HFSMTestController.cs      # 主测试控制器
├── HFSMTestSceneSetup.cs      # 场景设置助手
├── HFSMInitializer.cs         # 原有的初始化器（已存在）
└── README_HFSM_Testing.md     # 本文档
```

## 快速开始

### 1. 设置测试场景

**方法一：使用场景设置助手（推荐）**

1. 创建新的空场景
2. 创建一个空的GameObject，命名为"TestSceneSetup"
3. 添加`HFSMTestSceneSetup`组件
4. 在Inspector中右键点击组件标题，选择"设置测试场景"
5. 播放场景开始测试

**方法二：手动设置**

1. 创建包含以下组件的GameObject：
   - GameController
   - GameFSMCoordinator
   - WheelView + WheelFSM
   - LightView + LightFSM
2. 创建测试控制器GameObject，添加`HFSMTestController`组件
3. 配置组件之间的引用关系

### 2. 测试操作

#### 键盘控制说明

| 按键 | 功能 | 说明 |
|------|------|------|
| 1-5 | 执行测试步骤 | 按顺序执行游戏流程的各个步骤 |
| 空格 | 激活灯光 | 模拟用户点击灯光 |
| W | 转盘旋转(Move1) | 触发转盘旋转到Move1位置 |
| A/S/D | 转盘旋转(Move2/3/4) | 触发转盘旋转到其他位置 |
| R | 重置状态机 | 重置所有状态机到初始状态 |
| T | 切换自动测试 | 开启/关闭自动测试模式 |

#### 测试步骤详解

1. **步骤1 - 开始游戏**: 将游戏状态从Idle切换到FlashingLights
2. **步骤2 - 激活灯光**: 模拟用户点击，激活下一个灯光
3. **步骤3 - 旋转转盘**: 开始转盘旋转动画
4. **步骤4 - 移动潜艇**: 触发潜艇移动（待实现）
5. **步骤5 - 重置游戏**: 重置所有状态机到初始状态

## 高级功能

### 自动测试模式

自动测试模式会按照预设的时间间隔自动执行完整的游戏流程：

```csharp
// 在HFSMTestSceneSetup中配置
enableAutoTest = true;        // 启用自动测试
autoTestInterval = 3f;        // 每3秒执行一个步骤
```

### 事件监听和日志

测试控制器会自动监听并记录以下事件：
- 游戏状态变化
- FSM状态变化
- 转盘旋转完成
- 灯光激活

所有日志都会以`[HFSMTest]`前缀输出到控制台。

### 组件验证

使用场景设置助手的验证功能检查组件完整性：

```csharp
// 在Inspector中右键点击HFSMTestSceneSetup组件
// 选择"验证场景设置"
```

预期输出：
```
=== HFSM测试场景验证 ===
GameController: ✓
WheelView: ✓
LightView: ✓
GameFSMCoordinator: ✓
HFSMTestController: ✓
========================
```

## 自定义和扩展

### 添加新的测试步骤

```csharp
// 在HFSMTestController中添加新方法
public void TestStep6_CustomTest()
{
    LogDebug("=== 测试步骤6：自定义测试 ===");
    // 自定义测试逻辑
}

// 在HandleInput方法中添加键盘映射
if (Input.GetKeyDown(KeyCode.Alpha6))
{
    TestStep6_CustomTest();
}
```

### 自定义测试控制器

```csharp
public class CustomHFSMTestController : HFSMTestController
{
    protected override void TestStep4_MoveSubmarine()
    {
        LogDebug("=== 自定义潜艇移动测试 ===");
        // 自定义实现
        base.TestStep4_MoveSubmarine();
    }
}
```

### 添加事件监听

```csharp
// 在Awake中注册
this.RegisterEvent<CustomEvent>(OnCustomEvent);

// 在OnDestroy中注销
this.UnRegisterEvent<CustomEvent>(OnCustomEvent);

// 实现处理方法
private void OnCustomEvent(CustomEvent evt)
{
    LogDebug($"收到自定义事件: {evt.Data}");
}
```

## 故障排除

### 常见问题

**问题1: 组件未找到错误**
```
错误: "HFSMTestController: GameController未找到"
解决: 使用HFSMTestSceneSetup自动创建组件，或手动确保所有必要组件存在
```

**问题2: 状态切换失败**
```
错误: 状态机不响应切换命令
解决: 检查FSM初始化状态，验证事件系统是否正常工作
```

**问题3: 自动测试不启动**
```
错误: 自动测试模式无法启动
解决: 确保autoTest字段为true，检查协程是否正确启动
```

### 调试技巧

1. **启用详细日志**: 在HFSMTestController中设置`enableDebugLog = true`
2. **单步测试**: 使用数字键1-5逐步执行测试流程
3. **状态验证**: 观察控制台输出的状态变化日志
4. **组件检查**: 使用验证功能确保所有组件正确配置

## 最佳实践

1. **测试前准备**: 始终先验证场景设置的完整性
2. **逐步测试**: 先进行手动测试，确认无误后再使用自动测试
3. **日志分析**: 仔细观察状态切换的日志输出，确保流程正确
4. **定期重置**: 在测试过程中定期使用R键重置状态机
5. **文档更新**: 添加新功能时及时更新相关文档

## 相关文档

- [FSM状态切换指南](../Document/FSMStateTransitionGuide.md) - 详细的状态机切换逻辑
- [编程指南](../Document/ProgrammingGuidelines.md) - 项目编程规范
- [游戏设计文档](../Document/GameDesignDocument.md) - 游戏整体设计

---

**注意**: 这个测试框架是为开发和调试目的设计的，不应该包含在最终的发布版本中。 