using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using Sirenix.OdinInspector;
using QHLC.Models;
using QHLC.Events;
using DG.Tweening;
using QHLC.Utilities;
using QHLC.Controllers;
using QHLC.Managers;

namespace QHLC.Views
{
    /// <summary>
    /// 潜艇方向枚举
    /// </summary>
    public enum SubmarineDirection
    {
        LeftUp,    // 左上
        LeftDown,  // 左下
        RightDown, // 右下
        RightUp    // 右上
    }

    /// <summary>
    /// 潜艇视图，负责潜艇移动的简化接口
    /// 作为SubmarineManager的代理，提供简单的调用接口
    /// </summary>
    public class SubmarineView : MonoBehaviour, IController
    {
        private void Awake()
        {
            ModuleLogManager.LogSubmarine("SubmarineView.Awake: 初始化完成");
        }

        private void Start()
        {
            ModuleLogManager.LogSubmarine("SubmarineView.Start: 开始初始化");
            
            // 确保SubmarineManager已初始化
            if (SubmarineManager.Instance != null)
            {
                ModuleLogManager.LogSubmarine("SubmarineView.Start: SubmarineManager已就绪");
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineView.Start: SubmarineManager未找到");
            }
        }

        /// <summary>
        /// 移动潜艇指定步数（简化接口）
        /// </summary>
        /// <param name="steps">移动步数</param>
        public void MoveSteps(int steps)
        {
            ModuleLogManager.LogSubmarine($"SubmarineView.MoveSteps: 请求移动潜艇，步数: {steps}");
            
            // 使用静态方法，简化代码
            SubmarineManager.MoveSubmarineStatic(steps, () => {
                ModuleLogManager.LogSubmarine("SubmarineView.MoveSteps: 移动完成回调");
            });
        }

        /// <summary>
        /// 重置潜艇位置
        /// </summary>
        public void ResetSubmarine()
        {
            ModuleLogManager.LogSubmarine("SubmarineView.ResetSubmarine: 重置潜艇位置");
            
            // 使用静态方法，简化代码
            SubmarineManager.ResetSubmarinePositionStatic();
        }

        /// <summary>
        /// 获取当前站点索引
        /// </summary>
        public int GetCurrentStationIndex()
        {
            return SubmarineManager.GetCurrentStationIndexStatic();
        }

        /// <summary>
        /// 检查潜艇是否正在移动
        /// </summary>
        public bool IsMoving()
        {
            return SubmarineManager.IsMovingStatic();
        }

        /// <summary>
        /// 获取架构
        /// </summary>
        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }

        #region 调试方法
        /// <summary>
        /// 直接移动潜艇（调试用）
        /// </summary>
        [Button("直接移动潜艇(调试用)")]
        public void DirectMoveForDebug(int steps = 1)
        {
            ModuleLogManager.LogSubmarine($"SubmarineView.DirectMoveForDebug: 直接移动潜艇，步数: {steps}");
            MoveSteps(steps);
        }

        /// <summary>
        /// 调试重置潜艇
        /// </summary>
        [Button("重置潜艇位置(调试用)")]
        public void DirectResetForDebug()
        {
            ModuleLogManager.LogSubmarine("SubmarineView.DirectResetForDebug: 重置潜艇位置");
            ResetSubmarine();
        }

        /// <summary>
        /// 显示潜艇状态信息
        /// </summary>
        [Button("显示潜艇状态")]
        public void ShowSubmarineStatus()
        {
            if (SubmarineManager.Instance != null)
            {
                var manager = SubmarineManager.Instance;
                ModuleLogManager.LogSubmarine($"潜艇状态 - 当前站点: {manager.GetCurrentStationIndex()}, " +
                    $"是否移动中: {manager.IsMoving()}, " +
                    $"路径点数量: {manager.GetPathItemCount()}");
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManager未找到，无法显示状态");
            }
        }
        #endregion
    }
}
