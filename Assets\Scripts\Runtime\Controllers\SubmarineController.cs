using System.Collections;
using System.Collections.Generic;
using System.Linq; // 添加Linq命名空间，用于ToList()方法
using UnityEngine;
using UnityEngine.UI;
using QFramework;
using QHLC.Views;
using DG.Tweening;
using QHLC.Events;
using QHLC.Models;
using Tween = DG.Tweening.Tween;
using F8Framework.Core;

namespace QHLC
{
    /// <summary>
    /// 潜艇控制器，负责连接QFramework架构和Unity场景中的潜艇视图
    /// </summary>
    public class SubmarineController : MonoBehaviour, IController
    {
        [Header("潜艇设置")]
        [SerializeField] private RectTransform submarineRectTransform;
        [SerializeField] private Image submarineImage;
        [SerializeField] private float moveSpeed = 1000f;

        [Header("方向图片")]
        [SerializeField] private Sprite[] directionSprites;

        [Header("路径点")]
        [SerializeField] private QHLC.Models.PathItem[] pathItems;

        private void Awake()
        {
            // 注册事件
            TypeEventSystem.Global.Register<SubmarineResetEvent>(OnSubmarineReset);
        }

        private void Start()
        {
            InitializePathItems();
        }

        private void OnDestroy()
        {
            // 注销QFramework事件
            TypeEventSystem.Global.UnRegister<SubmarineResetEvent>(OnSubmarineReset);

            // 取消所有动画
            DOTween.Kill(submarineRectTransform);
        }

        /// <summary>
        /// 初始化路径点数据
        /// </summary>
        private void InitializePathItems()
        {
            if (pathItems == null || pathItems.Length == 0)
            {
                LogF8.LogError("路径点数组为空，无法初始化路径点数据");
                return;
            }

            // 直接使用PathItem列表，不再需要转换为PathItemData
            // 发送初始化路径点命令
            this.SendCommand(new InitializePathItemsCommand(pathItems.ToList()));

            // 重置潜艇位置
            this.SendCommand<ResetSubmarineCommand>();
        }

        /// <summary>
        /// 处理潜艇重置事件
        /// </summary>
        private void OnSubmarineReset(QHLC.Events.SubmarineResetEvent e)
        {
            // 取消所有动画
            DOTween.Kill(submarineRectTransform);

            // 重置潜艇位置
            var model = this.GetModel<SubmarineModel>();
            var pathItems = model.GetPathItems();

            if (pathItems.Count > 0)
            {
                submarineRectTransform.anchoredPosition = pathItems[0].Position;

                // 设置初始方向
                UpdateSubmarineSprite(SubmarineDirection.RightUp);
            }
        }

        // MoveAlongPath方法已移除，潜艇移动逻辑现在完全由PathKit实现

        /// <summary>
        /// 更新潜艇方向图片
        /// </summary>
        private void UpdateSubmarineSprite(SubmarineDirection direction)
        {
            if (submarineImage != null && directionSprites != null && directionSprites.Length > 0)
            {
                int directionIndex = (int)direction;
                if (directionIndex >= 0 && directionIndex < directionSprites.Length)
                {
                    submarineImage.sprite = directionSprites[directionIndex];

                    // 使用Command更新模型中的方向
                    this.SendCommand(new UpdateSubmarineDirectionCommand(direction));
                }
            }
        }

        /// <summary>
        /// 高亮路径点
        /// </summary>
        private void HighlightPathToTarget(int startStationIndex, int stepsToMove)
        {
            var model = this.GetModel<SubmarineModel>();
            var configService = this.GetUtility<QHLC.Common.ConfigurationService>();
            var config = configService.GetConfig<QHLC.ScriptableObjects.SubmarineConfig>();

            // 使用PathItem中的静态方法高亮路径
            PathItem.HighlightPathToTarget(
                model.GetPathItems(),  // 从模型获取所有路径点
                startStationIndex,     // 起始站点索引
                stepsToMove,           // 移动步数
                config                 // 潜艇配置
            );

            // 获取所有站点索引
            List<int> stationIndices = new List<int>();
            var allPathItems = model.GetPathItems();

            for (int i = 0; i < allPathItems.Count; i++)
            {
                if (allPathItems[i].isStation)
                {
                    stationIndices.Add(allPathItems[i].stationIndex);
                }
            }

            if (stationIndices.Count == 0) return;

            // 找到当前站点在列表中的位置
            int startIndex = stationIndices.IndexOf(startStationIndex);
            if (startIndex < 0) return;

            // 计算目标站点在列表中的位置
            int targetIndex = (startIndex + stepsToMove) % stationIndices.Count;
            if (targetIndex < 0) targetIndex += stationIndices.Count;

            // 收集要高亮的站点索引
            List<int> pathStationIndices = new List<int>();

            // 如果目标索引大于起始索引，直接添加中间的站点
            if (targetIndex > startIndex)
            {
                for (int i = startIndex + 1; i <= targetIndex; i++)
                {
                    pathStationIndices.Add(stationIndices[i]);
                }
            }
            // 如果目标索引小于起始索引，需要绕一圈
            else if (targetIndex < startIndex)
            {
                // 先添加从起始点到末尾的站点
                for (int i = startIndex + 1; i < stationIndices.Count; i++)
                {
                    pathStationIndices.Add(stationIndices[i]);
                }

                // 再添加从开始到目标点的站点
                for (int i = 0; i <= targetIndex; i++)
                {
                    pathStationIndices.Add(stationIndices[i]);
                }
            }

            // 对路径点应用闪烁效果
            foreach (int stationIndex in pathStationIndices)
            {
                int pathIndex = model.FindPathIndexByStationIndex(stationIndex);
                if (pathIndex >= 0 && pathIndex < pathItems.Length)
                {
                    // 调用PlayShine方法使站点闪烁
                    pathItems[pathIndex].PlayShine();
                }
            }
        }

        /// <summary>
        /// 获取架构
        /// </summary>
        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }
    }
}
