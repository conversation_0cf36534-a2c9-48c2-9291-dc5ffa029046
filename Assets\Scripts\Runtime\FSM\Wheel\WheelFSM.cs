using UnityEngine;
using F8Framework.Core;
using F8Framework.Launcher;
using QHLC.Views;
using QHLC.Events;
using QFramework;
using QHLC.Utilities;
using Sirenix.OdinInspector;
using QHLC.FSM.HLight;

namespace QHLC.FSM.Wheel
{
    /// <summary>
    /// 转盘状态机，管理转盘的不同状态
    /// </summary>
    public class WheelFSM : MonoBehaviour
    {
        [SerializeField] private WheelView wheelView;
        private HFSM<WheelStateType> wheelFSM;
        private LightView lightView;
        private bool isInitialized = false;
        
        // 保存状态引用以便后续访问
        private WheelIdleHState idleState;

        [ShowInInspector, ReadOnly, PropertyOrder(-10)]
        [BoxGroup("FSM 状态")]
        [LabelText("当前状态")]
        private string CurrentStateName => wheelFSM?.CurrentStateId.ToString() ?? "未初始化";

        [ShowInInspector, ReadOnly, PropertyOrder(-9)]
        [BoxGroup("FSM 状态")]
        [LabelText("上一个状态")]
        private WheelStateType PreviousStateId => wheelFSM?.PreviousStateId ?? WheelStateType.Idle;

        [ShowInInspector, ReadOnly, PropertyOrder(-8)]
        [BoxGroup("FSM 状态")]
        [LabelText("状态机名称")]
        private string FSMName => "WheelFSM";

        [ShowInInspector, ReadOnly, PropertyOrder(-7)]
        [BoxGroup("FSM 状态")]
        [LabelText("是否已初始化")]
        private bool IsInitialized => isInitialized;

        /// <summary>
        /// 获取当前状态ID，供外部访问
        /// </summary>
        public WheelStateType? CurrentStateId => wheelFSM?.CurrentStateId;

        private void Awake()
        {
            // 如果没有在Inspector中设置，则尝试从组件获取
            if (wheelView == null)
            {
                wheelView = GetComponent<WheelView>();
            }
            
            if (wheelView == null)
            {
                ModuleLogManager.LogWheelError("WheelFSM: 无法获取WheelView组件");
                return;
            }

            // 查找LightView
            lightView = FindObjectOfType<LightView>();
            if (lightView == null)
            {
                ModuleLogManager.LogWheelError("WheelFSM: 无法获取LightView组件");
                return;
            }

            // 注册FSM状态变化事件监听
            TypeEventSystem.Global.Register<FSMStateChangedEvent>(OnFSMStateChanged);
        }

        /// <summary>
        /// 处理FSM状态变化事件
        /// </summary>
        private void OnFSMStateChanged(FSMStateChangedEvent evt)
        {
            // 只处理游戏主FSM的状态变化
            if (evt.FSMType != FSMType.Game)
            {
                return;
            }

            if (evt.CurrentState == "FlashingLights")
            {
                // 当游戏状态切换到FlashingLights时，切换到Idle状态以激活嵌套LightFSM
                if (wheelFSM.CurrentStateId != WheelStateType.Idle)
                {
                    ModuleLogManager.LogWheel("WheelFSM: 响应FlashingLights状态，切换到Idle状态以激活嵌套LightFSM");
                    wheelFSM.ChangeState(WheelStateType.Idle);
                }
                
                // 通知Idle状态内的LightFSM开始激活
                if (idleState != null)
                {
                    ModuleLogManager.LogWheel("WheelFSM: 通知IdleState激活嵌套LightFSM");
                    idleState.ActivateLight();
                }
            }
            else if (evt.CurrentState == "WheelSpinning")
            {
                // 当游戏状态切换到WheelSpinning时，切换到Spinning状态
                if (wheelFSM.CurrentStateId == WheelStateType.Idle)
                {
                    ModuleLogManager.LogWheel("WheelFSM: 响应WheelSpinning状态，从Idle切换到Spinning");
                    wheelFSM.ChangeState(WheelStateType.Spinning);
                }
            }
        }

        public void Initialize()
        {
            if (isInitialized)
            {
                return;
            }

            // 创建转盘状态机
            wheelFSM = new HFSM<WheelStateType>();

            // 创建WheelIdle状态并添加LightFSM子状态机
            idleState = new WheelIdleHState(wheelFSM, wheelView, lightView);
            
            // 创建其他状态
            var spinningState = new WheelSpinningHState(wheelFSM, wheelView);
            var flashingState = new WheelFlashingHState(wheelFSM, wheelView);

            // 添加状态到状态机
            wheelFSM.AddState(WheelStateType.Idle, idleState);
            wheelFSM.AddState(WheelStateType.Spinning, spinningState);
            wheelFSM.AddState(WheelStateType.Flashing, flashingState);

            // 注册状态变化事件
            wheelFSM.OnStateChanged((prev, next) =>
            {
                ModuleLogManager.LogWheel($"WheelFSM: 状态从 {prev} 变为 {next}");
                
                // 发送FSM状态变化事件，通知其他FSM
                TypeEventSystem.Global.Send(new FSMStateChangedEvent
                {
                    FSMType = FSMType.Wheel,
                    PreviousState = prev.ToString(),
                    CurrentState = next.ToString()
                });
            });

            isInitialized = true;
        }

        /// <summary>
        /// 启动状态机
        /// </summary>
        public void StartFSM()
        {
            if (!isInitialized)
            {
                Initialize();
            }
            
            wheelFSM.StartState(WheelStateType.Idle);
        }

        private void Update()
        {
            if (wheelFSM != null && isInitialized)
            {
                wheelFSM.Update();
            }
        }

        private void OnDestroy()
        {
            // 关闭状态机
            if (wheelFSM != null)
            {
                wheelFSM.Clear();
                wheelFSM = null;
            }

            // 取消注册FSM状态变化事件监听
            TypeEventSystem.Global.UnRegister<FSMStateChangedEvent>(OnFSMStateChanged);
        }

        /// <summary>
        /// 开始旋转转盘
        /// </summary>
        public void StartSpinning(WheelResult targetResult)
        {
            // 只有在Idle状态下才能切换到Spinning
            if (wheelFSM.CurrentStateId == WheelStateType.Idle)
            {
                var model = wheelView.GetModel<WheelModel>();
                model.TargetResult.Value = targetResult;
                ModuleLogManager.LogWheel($"WheelFSM.StartSpinning: 使用模型中的TargetResult: {model.TargetResult.Value} ({(int)model.TargetResult.Value})");
                wheelFSM.ChangeState(WheelStateType.Spinning);
            }
            else
            {
                ModuleLogManager.LogWheel("WheelFSM: 转盘当前不在闲置状态，无法开始旋转");
            }
        }

        /// <summary>
        /// 切换到指定状态
        /// </summary>
        public void ChangeState(WheelStateType state)
        {
            if (wheelFSM != null)
            {
                wheelFSM.ChangeState(state);
            }
        }

        /// <summary>
        /// 重置转盘
        /// </summary>
        public void ResetWheel()
        {
            wheelFSM.ChangeState(WheelStateType.Idle);
        }
    }
}

