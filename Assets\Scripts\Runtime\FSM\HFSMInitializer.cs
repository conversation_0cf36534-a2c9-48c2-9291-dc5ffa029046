using UnityEngine;
using QFramework;
using QHLC.Views;
using QHLC.FSM.Wheel;
using QHLC.FSM.HLight;
using F8Framework.Core;

namespace QHLC.FSM
{
    /// <summary>
    /// HFSM初始化器，用于初始化和测试HFSM状态机
    /// </summary>
    public class HFSMInitializer : MonoBehaviour
    {
        [SerializeField] private WheelView wheelView;
        [SerializeField] private LightView lightView;

        private WheelFSM wheelFSM;

        private void Start()
        {
            if (wheelView == null)
            {
                wheelView = FindObjectOfType<WheelView>();
                if (wheelView == null)
                {
                    LogF8.LogError("HFSMInitializer: 找不到WheelView组件");
                    return;
                }
            }

            if (lightView == null)
            {
                lightView = FindObjectOfType<LightView>();
                if (lightView == null)
                {
                    LogF8.LogError("HFSMInitializer: 找不到LightView组件");
                    return;
                }
            }

            // 获取WheelFSM组件
            wheelFSM = wheelView.GetComponent<WheelFSM>();
            if (wheelFSM == null)
            {
                wheelFSM = wheelView.gameObject.AddComponent<WheelFSM>();
            }

            // 初始化WheelFSM
            wheelFSM.Initialize();
            
            // 启动WheelFSM
            wheelFSM.StartFSM();

            LogF8.Log("HFSMInitializer: HFSM初始化完成");
        }

        private void Update()
        {
            // 监听空格键，用于测试灯光输入
            if (Input.GetKeyDown(KeyCode.Space))
            {
                var inputBufferSystem = lightView.GetArchitecture().GetSystem<InputBufferSystem>();
                if (inputBufferSystem != null)
                {
                    // 创建一个灯光激活输入事件，索引为-1表示激活下一个未激活的灯光
                    var inputEvent = new InputEvent
                    {
                        Type = InputType.LightActivation,
                        Index = -1,
                        Timestamp = Time.time
                    };
                    inputBufferSystem.RecordInput(inputEvent);
                    LogF8.Log("HFSMInitializer: 记录空格键输入");
                }
            }

            // 监听W键，用于测试转盘旋转
            if (Input.GetKeyDown(KeyCode.W))
            {
                wheelFSM.StartSpinning(WheelResult.Move1);
                LogF8.Log("HFSMInitializer: 开始转盘旋转 - Move1");
            }
        }
    }
} 
