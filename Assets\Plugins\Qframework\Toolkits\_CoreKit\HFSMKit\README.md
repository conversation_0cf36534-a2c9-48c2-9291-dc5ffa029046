# HFSMKit - 分层有限状态机 (Hierarchical Finite State Machine)

## 简介
HFSMKit是QFramework框架的一个功能模块，提供分层有限状态机的实现。HFSM允许状态嵌套子状态机，使状态管理更加结构化和可扩展，非常适合复杂的游戏行为模式实现。

## 特性
- 支持状态嵌套子状态机
- 提供链式API和类继承两种使用方式
- 支持状态路径获取
- 自动管理状态的生命周期
- 兼容现有的QFramework FSM API风格

## 基本用法

### 链式API风格

```csharp
// 创建主状态机
var mainFSM = new HFSM<MainStateEnum>();

// 获取状态并配置
var moveState = mainFSM.State(MainStateEnum.Move)
    .OnEnter(() => { Debug.Log("进入移动状态"); })
    .OnExit(() => { Debug.Log("离开移动状态"); });

// 创建子状态机
var moveSubFSM = moveState.CreateSubStateMachine();

// 配置子状态
moveSubFSM.State(MoveSubStateEnum.Walk)
    .OnEnter(() => { Debug.Log("进入行走子状态"); })
    .OnExit(() => { Debug.Log("离开行走子状态"); });

// 启动主状态机
mainFSM.StartState(MainStateEnum.Move);

// 在MonoBehaviour中更新状态机
void Update()
{
    mainFSM.Update();
}
```

### 类继承风格

```csharp
// 主状态类
public class PatrolState : AbstractHState<MainStateEnum, MonoComponent>
{
    private HFSM<PatrolSubStateEnum> mSubFSM;

    public PatrolState(HFSM<MainStateEnum> fsm, MonoComponent target) : base(fsm, target)
    {
        // 创建子状态机
        mSubFSM = CreateSubStateMachine();
        
        // 添加子状态
        mSubFSM.AddState(PatrolSubStateEnum.Idle, new PatrolIdleState(mSubFSM, target));
    }

    protected override void OnEnter()
    {
        Debug.Log("进入巡逻状态");
        mSubFSM.StartState(PatrolSubStateEnum.Idle);
    }
}

// 巡逻子状态类
public class PatrolIdleState : AbstractHState<PatrolSubStateEnum, MonoComponent>
{
    public PatrolIdleState(HFSM<PatrolSubStateEnum> fsm, MonoComponent target) : base(fsm, target) { }

    protected override void OnEnter()
    {
        Debug.Log("进入巡逻-待机子状态");
    }
}
```

## 主要组件

### IHFSM接口
定义分层状态机的基本功能。

### IHState接口
扩展IState接口，增加分层特性。

### HFSM<T>类
分层有限状态机实现，支持状态嵌套和层级管理。

### HState<T>类
分层状态实现，支持子状态机创建。

### AbstractHState<TStateId, TTarget>类
状态抽象基类，方便使用类继承方式创建状态。

## 示例代码
请参考以下示例：
- HFSMExample.cs - 链式API示例
- HFSMClassExample.cs - 类继承示例

## 注意事项
- 确保在MonoBehaviour的Update方法中调用状态机的Update方法
- 在OnDestroy中调用状态机的Clear方法释放资源
- 子状态机创建后需要调用StartState设置初始状态 