# SubmarineManager 使用说明

## 概述

SubmarineManager 是一个单例管理器，用于简化潜艇移动系统的调用链。它替代了之前复杂的事件链和Command模式，提供了更直接、更可靠的潜艇控制接口。

## 架构变更

### 之前的复杂调用链
```
外部触发 -> StartSubmarineMovingEvent -> MoveSubmarineCommand -> SubmarineSystem -> SubmarineModel -> SubmarineView -> PathController
```

### 现在的简化调用链
```
外部触发 -> SubmarineManager.Instance.MoveSubmarine() -> 直接执行移动
```

## 主要功能

### 1. 移动潜艇
```csharp
// 移动潜艇指定步数
SubmarineManager.Instance.MoveSubmarine(steps, onComplete);

// 示例
SubmarineManager.Instance.MoveSubmarine(2, () => {
    Debug.Log("移动完成！");
});
```

### 2. 重置潜艇位置
```csharp
// 重置潜艇到起始位置
SubmarineManager.Instance.ResetSubmarinePosition();
```

### 3. 获取状态信息
```csharp
// 获取当前站点索引
int currentStation = SubmarineManager.Instance.GetCurrentStationIndex();

// 检查是否正在移动
bool isMoving = SubmarineManager.Instance.IsMoving();

// 获取路径点数量
int pathCount = SubmarineManager.Instance.GetPathItemCount();
```

### 4. 手动初始化
```csharp
// 如果需要重新初始化
SubmarineManager.Instance.InitializeSubmarine();
```

## 使用示例

### 基本使用
```csharp
using QHLC.Managers;

public class MyController : MonoBehaviour
{
    public void MoveSubmarine()
    {
        // 检查管理器是否存在
        if (SubmarineManager.Instance == null)
        {
            Debug.LogError("SubmarineManager未找到");
            return;
        }

        // 检查是否正在移动
        if (SubmarineManager.Instance.IsMoving())
        {
            Debug.LogWarning("潜艇正在移动中");
            return;
        }

        // 移动潜艇
        SubmarineManager.Instance.MoveSubmarine(1, () => {
            Debug.Log("移动完成");
        });
    }
}
```

### 键盘控制示例
```csharp
private void Update()
{
    if (Input.GetKeyDown(KeyCode.Space))
    {
        SubmarineManager.Instance?.MoveSubmarine(1);
    }
    
    if (Input.GetKeyDown(KeyCode.R))
    {
        SubmarineManager.Instance?.ResetSubmarinePosition();
    }
}
```

## 设置和配置

### 1. 自动组件查找
SubmarineManager 会自动查找场景中的以下组件：
- SubmarineView（潜艇视图）
- PathItem[]（路径点数组）
- SubmarineFSM（状态机）
- PathKitSettings（路径设置）

### 2. 手动设置组件
如果自动查找失败，可以在 SubmarineView 中使用编辑器按钮：
- "获取PathItem组件" - 自动获取路径点
- "设置SubmarineManager组件" - 将当前组件设置到管理器

### 3. Inspector 设置
在 SubmarineManager 的 Inspector 中可以手动设置：
- submarineRectTransform（潜艇RectTransform）
- submarineImage（潜艇图片）
- directionSprites（方向精灵数组）
- pathItems（路径点数组）
- pathKitSettings（PathKit设置）
- submarineFSM（状态机）

## 调试和测试

### 1. 使用 SubmarineManagerTest
在场景中添加 `SubmarineManagerTest` 组件，提供以下测试功能：
- 测试移动潜艇
- 重置潜艇位置
- 显示潜艇状态
- 初始化管理器
- 连续移动测试

### 2. 使用 SimpleSubmarineController
添加 `SimpleSubmarineController` 组件可以：
- 使用空格键移动潜艇
- 使用R键重置潜艇
- 在屏幕上显示状态信息

### 3. 日志输出
所有操作都会通过 `ModuleLogManager.LogSubmarine()` 输出详细日志，便于调试。

## 错误处理

### 常见问题及解决方案

1. **SubmarineManager未找到**
   - 确保场景中有 SubmarineManager 组件
   - 或者调用 `SubmarineManager.Instance` 会自动创建

2. **路径点为空**
   - 检查场景中是否有 PathItem 组件
   - 使用 "获取PathItem组件" 按钮重新获取

3. **PathController初始化失败**
   - 检查 PathKitSettings 是否正确设置
   - 确保路径点数组不为空

4. **潜艇无法移动**
   - 检查 `IsMoving()` 状态
   - 确保路径点设置正确
   - 查看控制台日志输出

## 性能优化

1. **单例模式**：避免重复创建管理器实例
2. **组件缓存**：缓存常用组件引用，避免重复查找
3. **状态检查**：移动前检查状态，避免重复操作
4. **备用方案**：PathController失败时自动使用DOTween备用方案

## 兼容性

- 保持与 QFramework 架构的兼容性
- 保留关键事件（如 SubmarineMoveCompletedEvent）供其他模块监听
- 支持现有的 FSM 状态管理
- 向后兼容原有的 SubmarineView 接口

## 迁移指南

### 从旧系统迁移到新系统

1. **替换事件调用**
   ```csharp
   // 旧方式
   TypeEventSystem.Global.Send(new StartSubmarineMovingEvent { Steps = 1 });
   
   // 新方式
   SubmarineManager.Instance.MoveSubmarine(1);
   ```

2. **替换Command调用**
   ```csharp
   // 旧方式
   this.SendCommand(new MoveSubmarineCommand(steps));
   
   // 新方式
   SubmarineManager.Instance.MoveSubmarine(steps);
   ```

3. **简化初始化**
   ```csharp
   // 旧方式：复杂的初始化逻辑
   // 新方式：自动初始化或调用 InitializeSubmarine()
   ```

通过这种重构，潜艇移动系统变得更加简单、可靠和易于维护。 