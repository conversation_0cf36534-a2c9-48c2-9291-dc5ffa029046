
using UnityEngine;
using Sirenix.OdinInspector;
using QHLC.Managers;
using QHLC.Utilities;

namespace QHLC.Test
{
    /// <summary>
    /// SubmarineManager测试脚本
    /// 用于测试简化后的潜艇移动功能
    /// </summary>
    public class SubmarineManagerTest : MonoBehaviour
    {
        [Header("测试参数")]
        [SerializeField] private int testSteps = 1;

        [But<PERSON>("测试移动潜艇", ButtonSizes.Large)]
        public void TestMoveSubmarine()
        {
            ModuleLogManager.LogSubmarine($"SubmarineManagerTest.TestMoveSubmarine: 测试移动潜艇，步数: {testSteps}");

            SubmarineManager.MoveSubmarineStatic(testSteps, () =>
            {
                ModuleLogManager.LogSubmarine("SubmarineManagerTest: 移动完成回调");
            });
    
        }

        [Button("重置潜艇位置", ButtonSizes.Large)]
        public void TestResetSubmarine()
        {
            ModuleLogManager.LogSubmarine("SubmarineManagerTest.TestResetSubmarine: 测试重置潜艇位置");
            
            if (SubmarineManager.Instance != null)
            {
                SubmarineManager.Instance.ResetSubmarinePosition();
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManagerTest: SubmarineManager未找到");
            }
        }

        [Button("显示潜艇状态", ButtonSizes.Large)]
        public void TestShowStatus()
        {
            if (SubmarineManager.Instance != null)
            {
                var manager = SubmarineManager.Instance;
                ModuleLogManager.LogSubmarine($"潜艇状态信息:\n" +
                    $"- 当前站点索引: {manager.GetCurrentStationIndex()}\n" +
                    $"- 是否正在移动: {manager.IsMoving()}\n" +
                    $"- 路径点数量: {manager.GetPathItemCount()}");
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManagerTest: SubmarineManager未找到");
            }
        }

        [Button("初始化SubmarineManager", ButtonSizes.Large)]
        public void TestInitializeManager()
        {
            ModuleLogManager.LogSubmarine("SubmarineManagerTest.TestInitializeManager: 手动初始化SubmarineManager");
            
            if (SubmarineManager.Instance != null)
            {
                SubmarineManager.Instance.InitializeSubmarine();
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManagerTest: SubmarineManager未找到");
            }
        }

        [Button("连续移动测试", ButtonSizes.Large)]
        public void TestContinuousMove()
        {
            ModuleLogManager.LogSubmarine("SubmarineManagerTest.TestContinuousMove: 开始连续移动测试");
            
            if (SubmarineManager.Instance != null)
            {
                StartCoroutine(ContinuousMoveCoroutine());
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineManagerTest: SubmarineManager未找到");
            }
        }

        private System.Collections.IEnumerator ContinuousMoveCoroutine()
        {
            var manager = SubmarineManager.Instance;
            
            for (int i = 0; i < 5; i++)
            {
                ModuleLogManager.LogSubmarine($"连续移动测试 - 第{i + 1}次移动");
                
                bool moveCompleted = false;
                manager.MoveSubmarine(1, () => {
                    moveCompleted = true;
                });
                
                // 等待移动完成
                while (!moveCompleted)
                {
                    yield return new WaitForSeconds(0.1f);
                }
                
                // 等待一秒再进行下一次移动
                yield return new WaitForSeconds(1.0f);
            }
            
            ModuleLogManager.LogSubmarine("连续移动测试完成");
        }

        [Header("运行时信息")]
        [SerializeField] private bool _dummyField; // 占位字段，确保Header有效

        [ShowInInspector, ReadOnly]
        public bool ManagerExists => SubmarineManager.Instance != null;

        [ShowInInspector, ReadOnly]
        public int CurrentStation => SubmarineManager.Instance?.GetCurrentStationIndex() ?? -1;

        [ShowInInspector, ReadOnly]
        public bool IsMoving => SubmarineManager.Instance?.IsMoving() ?? false;

        [ShowInInspector, ReadOnly]
        public int PathItemCount => SubmarineManager.Instance?.GetPathItemCount() ?? 0;
    }
} 
