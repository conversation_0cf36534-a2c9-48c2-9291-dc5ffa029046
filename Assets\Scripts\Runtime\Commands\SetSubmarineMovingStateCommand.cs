using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;

namespace QHLC
{
    /// <summary>
    /// 设置潜艇移动状态命令
    /// </summary>
    public class SetSubmarineMovingStateCommand : AbstractCommand
    {
        private readonly bool _isMoving;

        public SetSubmarineMovingStateCommand(bool isMoving)
        {
            _isMoving = isMoving;
        }

        protected override void OnExecute()
        {
            // 调用系统方法设置移动状态
            this.GetSystem<SubmarineSystem>().SetMovingState(_isMoving);
        }
    }
} 