using UnityEngine;
using QHLC.Managers;
using QHLC.Utilities;

namespace QHLC.Examples
{
    /// <summary>
    /// 简单的潜艇控制器示例
    /// 展示如何使用新的SubmarineManager单例进行潜艇控制
    /// </summary>
    public class SimpleSubmarineController : MonoBehaviour
    {
        [Header("控制设置")]
        [SerializeField] private KeyCode moveKey = KeyCode.Space;
        [SerializeField] private KeyCode resetKey = KeyCode.R;
        [SerializeField] private int moveSteps = 1;

        private void Update()
        {
            // 按空格键移动潜艇
            if (Input.GetKeyDown(moveKey))
            {
                MoveSubmarine();
            }

            // 按R键重置潜艇
            if (Input.GetKeyDown(resetKey))
            {
                ResetSubmarine();
            }
        }

        /// <summary>
        /// 移动潜艇
        /// </summary>
        public void MoveSubmarine()
        {
            // 检查潜艇是否正在移动
            if (SubmarineManager.IsMovingStatic())
            {
                ModuleLogManager.LogSubmarineWarning("SimpleSubmarineController: 潜艇正在移动中，请等待");
                return;
            }

            ModuleLogManager.LogSubmarine($"SimpleSubmarineController: 开始移动潜艇，步数: {moveSteps}");

            // 使用静态方法，简化代码
            SubmarineManager.MoveSubmarineStatic(moveSteps, () => {
                ModuleLogManager.LogSubmarine("SimpleSubmarineController: 潜艇移动完成");
                OnSubmarineMovementCompleted();
            });
        }

        /// <summary>
        /// 重置潜艇位置
        /// </summary>
        public void ResetSubmarine()
        {
            ModuleLogManager.LogSubmarine("SimpleSubmarineController: 重置潜艇位置");
            // 使用静态方法，简化代码
            SubmarineManager.ResetSubmarinePositionStatic();
        }

        /// <summary>
        /// 潜艇移动完成回调
        /// </summary>
        private void OnSubmarineMovementCompleted()
        {
            // 在这里可以添加移动完成后的逻辑
            // 例如：播放音效、更新UI、触发其他事件等

            var currentStation = SubmarineManager.GetCurrentStationIndexStatic();
            ModuleLogManager.LogSubmarine($"SimpleSubmarineController: 潜艇到达站点 {currentStation}");

            // 示例：如果到达特定站点，执行特殊逻辑
            if (currentStation == 0)
            {
                ModuleLogManager.LogSubmarine("SimpleSubmarineController: 潜艇回到起点！");
            }
        }

        /// <summary>
        /// 获取潜艇状态信息
        /// </summary>
        public void GetSubmarineStatus()
        {
            ModuleLogManager.LogSubmarine($"潜艇状态:\n" +
                $"当前站点: {SubmarineManager.GetCurrentStationIndexStatic()}\n" +
                $"是否移动中: {SubmarineManager.IsMovingStatic()}\n" +
                $"路径点数量: {(SubmarineManager.Instance?.GetPathItemCount() ?? 0)}");
        }

        /// <summary>
        /// 移动到指定站点
        /// </summary>
        /// <param name="targetStation">目标站点索引</param>
        public void MoveToStation(int targetStation)
        {
            int currentStation = SubmarineManager.GetCurrentStationIndexStatic();
            int pathCount = SubmarineManager.Instance?.GetPathItemCount() ?? 0;

            if (pathCount == 0)
            {
                ModuleLogManager.LogSubmarineError("SimpleSubmarineController: 路径点数量为0");
                return;
            }

            // 计算需要移动的步数
            int steps;
            if (targetStation >= currentStation)
            {
                steps = targetStation - currentStation;
            }
            else
            {
                // 环形路径，需要绕一圈
                steps = (pathCount - currentStation) + targetStation;
            }

            if (steps > 0)
            {
                ModuleLogManager.LogSubmarine($"SimpleSubmarineController: 移动到站点 {targetStation}，需要 {steps} 步");
                SubmarineManager.MoveSubmarineStatic(steps, () => {
                    ModuleLogManager.LogSubmarine($"SimpleSubmarineController: 成功到达站点 {targetStation}");
                });
            }
            else
            {
                ModuleLogManager.LogSubmarine("SimpleSubmarineController: 已经在目标站点");
            }
        }

        private void OnGUI()
        {
            // 简单的GUI显示控制说明
            GUI.Label(new Rect(10, 10, 300, 20), $"按 {moveKey} 键移动潜艇");
            GUI.Label(new Rect(10, 30, 300, 20), $"按 {resetKey} 键重置潜艇");

            // 使用静态方法获取状态信息
            GUI.Label(new Rect(10, 60, 300, 20), $"当前站点: {SubmarineManager.GetCurrentStationIndexStatic()}");
            GUI.Label(new Rect(10, 80, 300, 20), $"移动状态: {(SubmarineManager.IsMovingStatic() ? "移动中" : "静止")}");
            GUI.Label(new Rect(10, 100, 300, 20), $"路径点数: {(SubmarineManager.Instance?.GetPathItemCount() ?? 0)}");
        }
    }
} 