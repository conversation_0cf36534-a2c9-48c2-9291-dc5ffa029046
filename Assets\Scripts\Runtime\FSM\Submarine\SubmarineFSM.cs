using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using F8Framework.Core;
using QHLC.Controllers;
using QHLC.Models;
using QHLC.Views;
using F8Framework.Launcher;
using QFramework;
using QHLC.Events;
using System;
using QHLC.Utilities;
using Sirenix.OdinInspector;

namespace QHLC.FSM
{
    /// <summary>
    /// 潜艇状态机管理器，使用QFramework的HFSM系统管理潜艇状态
    /// </summary>
    public class SubmarineFSM : MonoBehaviour
    {
        [SerializeField] public SubmarineView submarineView;

        private HFSM<SubmarineStateType> submarineHFSM;

        // 用于记录上一个状态
        private SubmarineStateType lastPreviousState;

        [ShowInInspector, ReadOnly, PropertyOrder(-10)]
        [BoxGroup("FSM 状态")]
        [LabelText("当前状态")]
        private string CurrentStateName => submarineHFSM?.CurrentStateId.ToString() ?? "未初始化";

        [ShowInInspector, ReadOnly, PropertyOrder(-9)]
        [BoxGroup("FSM 状态")]
        [LabelText("上一个状态")]
        private string PreviousStateName => lastPreviousState.ToString();

        [ShowInInspector, ReadOnly, PropertyOrder(-8)]
        [BoxGroup("FSM 状态")]
        [LabelText("状态机名称")]
        private string FSMName => "SubmarineHFSM";

        [ShowInInspector, ReadOnly, PropertyOrder(-7)]
        [BoxGroup("FSM 状态")]
        [LabelText("是否已初始化")]
        private bool IsInitialized => submarineHFSM != null;

        /// <summary>
        /// 获取当前状态ID，供外部访问
        /// </summary>
        public SubmarineStateType? CurrentStateId => submarineHFSM?.CurrentStateId;

        private void Awake()
        {
            if (submarineView == null)
            {
                submarineView = GetComponent<SubmarineView>();
                if (submarineView == null)
                {
                    ModuleLogManager.LogSubmarineError("SubmarineFSM: SubmarineView not found!");
                    return;
                }
            }

            // 注册FSM状态变化事件监听
            TypeEventSystem.Global.Register<FSMStateChangedEvent>(OnFSMStateChanged);
        }

        /// <summary>
        /// 处理FSM状态变化事件
        /// </summary>
        private void OnFSMStateChanged(FSMStateChangedEvent evt)
        {
            // 只处理来自主游戏FSM的事件
            if (evt.FSMType != FSMType.Game) return;

            ModuleLogManager.LogSubmarine($"SubmarineFSM: 收到GameFSM状态变化事件 - 从 {evt.PreviousState} 到 {evt.CurrentState}");

            // 根据主游戏FSM的状态变化调整自己的状态
            // 注意：这里的逻辑应该与GameFSM中的CoordinateOtherFSMs方法一致
            // 这里只是一个备用机制，以防GameFSM的协调失败
        }

        public void Initialize()
        {
            // 创建HFSM
            submarineHFSM = new HFSM<SubmarineStateType>();

            // 创建状态实例
            var idleState = new SubmarineIdleHState(submarineHFSM, submarineView);
            var movingState = new SubmarineMovingHState(submarineHFSM, submarineView);
            var arrivedState = new SubmarineArrivedHState(submarineHFSM, submarineView);

            // 添加状态到状态机
            submarineHFSM.AddState(SubmarineStateType.Idle, idleState);
            submarineHFSM.AddState(SubmarineStateType.Moving, movingState);
            submarineHFSM.AddState(SubmarineStateType.Arrived, arrivedState);

            // 注册状态变化事件
            submarineHFSM.OnStateChanged(OnStateChanged);

            // 启动状态机
            submarineHFSM.StartState(SubmarineStateType.Idle);
        }

        public void StartFSM()
        {
            // 切换到默认状态
            if (submarineHFSM != null)
            {
                submarineHFSM.ChangeState(SubmarineStateType.Idle);
            }
        }

        private void Update()
        {
            // 更新HFSM
            submarineHFSM?.Update();
        }

        private void OnStateChanged(SubmarineStateType previousState, SubmarineStateType currentState)
        {
            // 更新上一个状态，用于Odin可视化
            lastPreviousState = previousState;

            ModuleLogManager.LogSubmarine($"SubmarineHFSM: 状态变化 - 从 {previousState} 到 {currentState}");

            // 发送FSM状态变化事件，通知其他FSM
            TypeEventSystem.Global.Send(new FSMStateChangedEvent
            {
                FSMType = FSMType.Submarine,
                PreviousState = previousState.ToString(),
                CurrentState = currentState.ToString()
            });
        }

        public void ChangeState(SubmarineStateType newState)
        {
            if (submarineHFSM != null)
            {
                submarineHFSM.ChangeState(newState);
            }
            else
            {
                ModuleLogManager.LogSubmarineError($"SubmarineHFSM: Cannot change to state {newState}, HFSM not initialized!");
            }
        }

        public SubmarineStateType GetCurrentState()
        {
            return submarineHFSM?.CurrentStateId ?? SubmarineStateType.Idle;
        }

        private void OnDestroy()
        {
            if (submarineHFSM != null)
            {
                submarineHFSM.Clear();
                submarineHFSM = null;
            }

            // 取消注册FSM状态变化事件监听
            TypeEventSystem.Global.UnRegister<FSMStateChangedEvent>(OnFSMStateChanged);
        }
    }
}
