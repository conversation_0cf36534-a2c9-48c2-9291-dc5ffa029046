using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using F8Framework.Core;
using QFramework;
using QHLC.Models;
using QHLC.Views;
using QHLC.FSM;
using QHLC.GameData;
using QHLC.Events;
using QHLC.FSM.Wheel;
using QHLC.FSM.HLight;

namespace QHLC.Controllers
{
    /// <summary>
    /// 游戏主控制器，负责协调Model和View之间的交互
    /// </summary>
    public class GameController : SingletonMono<GameController>, IController
    {
        [SerializeField] public LightView lightView;
        [SerializeField] public WheelView wheelView;
        [SerializeField] public SubmarineView submarineView;
        [SerializeField] private GameFSMCoordinator gameFSMCoordinator;
        [SerializeField] public WheelFSM wheelFSM;
        [SerializeField] public SubmarineFSM submarineFSM;
        [SerializeField] private bool enableDebugger = true;

        // 游戏模型
        public GameModel gameModel;

        /// <summary>
        /// 获取架构
        /// </summary>
        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }

        public void StartGame()
        {
            // 初始化游戏模型
            gameModel = new GameModel();

            // 注册事件监听
            RegisterEventListeners();

            // 重置游戏状态
            ResetGame();

            // 初始化各个组件的状态机
            InitializeFSMs();

            // 初始化调试器
            if (enableDebugger)
            {
                InitializeDebugger();
            }
        }

        // 注意：删除了 SetGameStateSimple 方法，因为它是未使用的

        private void RegisterEventListeners()
        {
            // 使用 TypeEventSystem 注册事件监听
            // 注意：这些事件处理应该移到相应的状态类中
            // 但暂时保留，以便更新模型中的数据和调试

            // 注册潜艇移动完成事件
            TypeEventSystem.Global.Register<SubmarineMoveCompletedEvent>(OnSubmarineMoveCompletedHandler);

            // 注册游戏状态变化事件
            TypeEventSystem.Global.Register<GameStateChangedEvent>(OnGameStateChangedHandler);
        }

        private void ResetGame()
        {
            // 重置游戏状态
            gameModel.ResetGame();

            // 重置视图
            ResetViews();

            // 游戏状态由FSM管理，不再使用 gameModel.CurrentState
        }
        private void ResetViews()
        {
            // 重置视图
            if (lightView != null)
            {
                lightView.ResetLights();
            }

            if (wheelView != null)
            {
                // 使用QFramework架构发送重置命令
                this.SendCommand<ResetWheelCommand>();
            }

            if (submarineView != null)
            {
                // 使用QFramework架构发送重置命令
                this.SendCommand<ResetSubmarineCommand>();
            }
        }

        // 潜艇移动完成事件处理
        private void OnSubmarineMoveCompletedHandler(SubmarineMoveCompletedEvent e)
        {
            // 更新模型中的潜艇位置
            var submarineModel = this.GetModel<SubmarineModel>();
            gameModel.SubmarinePosition = submarineModel.CurrentStationIndex.Value;
            LogF8.Log($"GameController: 潜艇移动完成，当前站点索引：{gameModel.SubmarinePosition}");

            // 注意：不再需要手动开始灯光闪烁
            // 当潜艇移动完成时，FSM会自动切换到FlashingLightsState
            LogF8.Log("GameController: 潜艇移动完成，等待FSM状态切换");
        }

        /// <summary>
        /// 初始化所有组件的状态机
        /// </summary>
        private void InitializeFSMs()
        {
            // 初始化游戏状态机协调器
            if (gameFSMCoordinator != null)
            {
                gameFSMCoordinator.Initialize();
                gameFSMCoordinator.StartGame();
            }
            else
            {
                LogF8.LogError("GameController: gameFSMCoordinator为null，无法初始化状态机协调器");
                gameFSMCoordinator = GetComponent<GameFSMCoordinator>();
                if (gameFSMCoordinator == null)
                {
                    gameFSMCoordinator = gameObject.AddComponent<GameFSMCoordinator>();
                }

                if (gameFSMCoordinator != null)
                {
                    gameFSMCoordinator.Initialize();
                    gameFSMCoordinator.StartGame();
                }
            }

            // 修复：不再初始化独立的LightFSM，因为它应该作为WheelFSM的嵌套状态机
            // LightFSM将由WheelFSM的IdleState管理
            // 
            // 原来的代码：
            // if (lightFSM != null && lightView != null)
            // {
            //     lightFSM.Initialize();
            //     lightFSM.StartFSM();
            // }
            // else if (lightView != null)
            // {
            //     LogF8.LogWarning("GameController: lightFSM为null，尝试添加LightHFSM组件");
            //     lightFSM = lightView.gameObject.GetComponent<LightHFSM>();
            //     if (lightFSM == null)
            //     {
            //         lightFSM = lightView.gameObject.AddComponent<LightHFSM>();
            //     }
            //
            //     if (lightFSM != null)
            //     {
            //         lightFSM.Initialize();
            //         lightFSM.StartFSM();
            //     }
            // }
            
            LogF8.Log("GameController: 跳过独立LightFSM初始化，将由WheelFSM的嵌套状态机处理");

            // 初始化转盘状态机
            if (wheelFSM != null && wheelView != null)
            {
                wheelFSM.Initialize();
                wheelFSM.StartFSM();
            }
            else if (wheelView != null)
            {
                LogF8.LogWarning("GameController: wheelFSM为null，尝试添加WheelFSM组件");
                wheelFSM = wheelView.gameObject.GetComponent<WheelFSM>();
                if (wheelFSM == null)
                {
                    wheelFSM = wheelView.gameObject.AddComponent<WheelFSM>();
                }

                if (wheelFSM != null)
                {
                    wheelFSM.Initialize();
                    wheelFSM.StartFSM();
                }
            }

            // 初始化潜艇状态机
            if (submarineFSM != null && submarineView != null)
            {
                submarineFSM.Initialize();
                submarineFSM.StartFSM();
            }
            else if (submarineView != null)
            {
                LogF8.LogWarning("GameController: submarineFSM为null，尝试添加SubmarineFSM组件");
                submarineFSM = submarineView.gameObject.GetComponent<SubmarineFSM>();
                if (submarineFSM == null)
                {
                    submarineFSM = submarineView.gameObject.AddComponent<SubmarineFSM>();
                }

                if (submarineFSM != null)
                {
                    submarineFSM.Initialize();
                    submarineFSM.StartFSM();
                }
            }
        }

        // 游戏状态变化事件处理
        private void OnGameStateChangedHandler(GameStateChangedEvent e)
        {
            LogF8.Log($"GameController: 游戏状态变化 - 从 {e.PreviousState} 到 {e.CurrentState}");

            // 更新游戏模型中的状态
            // 注意：游戏状态由GameFSMCoordinator管理，这里只是更新模型中的数据
        }

        // 该方法已经被删除，因为它与下面的SetGameState方法重复

        /// <summary>
        /// 初始化调试器
        /// </summary>
        private void InitializeDebugger()
        {
            // 检查是否已经有FSMDebugger组件
            var debugger = GetComponent<QHLC.Debug.FSMDebugger>();
            if (debugger == null)
            {
                // 添加FSMDebugger组件
                debugger = gameObject.AddComponent<QHLC.Debug.FSMDebugger>();
                LogF8.Log("GameController: 添加FSMDebugger组件");
            }
        }

        // 注意：删除了 StartLightFlashing 方法
        // 这个逻辑现在由 LightFSM 完全处理

        // 设置游戏状态 - 由FSM调用
        public void SetGameState(FSM.GameStateType newState)
        {
            // 游戏状态由FSM管理，不再使用 gameModel.CurrentState

            // 根据状态执行相应操作
            switch (newState)
            {
                case FSM.GameStateType.Idle:
                    // 闲置状态处理
                    break;
                case FSM.GameStateType.FlashingLights:
                    // 重置灯光，准备新一轮
                    if (lightView != null)
                    {
                        lightView.ResetLights();
                    }
                    break;
                case FSM.GameStateType.WheelSpinning:
                    // 开始转盘旋转由FSM处理
                    break;
                case FSM.GameStateType.SubmarineMoving:
                    // 潜艇移动状态由FSM处理
                    break;
            }
        }
    }
}
