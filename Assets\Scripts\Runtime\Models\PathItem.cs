using UnityEngine;
using Sirenix.OdinInspector;
using UnityEngine.UI;
using DG.Tweening;
using F8Framework.Core;
using QFramework;  // 添加QFramework命名空间引用
using System.Collections.Generic;
using QHLC.Common;
using QHLC.ScriptableObjects;
using QHLC.Utilities;
using System.Collections.Specialized; // 添加ModuleLogManager的命名空间

namespace QHLC.Models
{
    /// <summary>
    /// 路径点数据类，用于定义路径点的类型和属性
    /// 继承自QFramework.BasePathItem，以便与PathKit集成
    /// </summary>
    [System.Serializable]
    public class PathItem : BasePathItem  // 继承自BasePathItem
    {
        [Tooltip("路径点的RectTransform组件")]
        public RectTransform pointTransform;

        [Tooltip("路径点的图片组件")]
        public Image pointImage;

        // 兼容属性，映射到BasePathItem的属性
        [Tooltip("是否是站点（潜艇会在站点停留）")]
        public bool isStation
        {
            get { return PointType == PathPointType.Station; }
            set { PointType = value ? PathPointType.Station : PathPointType.Waypoint; }
        }

        // 兼容属性，映射到BasePathItem的属性
        [Tooltip("站点名称（仅当isStation为true时有效）")]
        public new string stationName
        {
            get { return StationName; }
            set { StationName = value; }
        }

        // 兼容属性，映射到BasePathItem的属性
        [Tooltip("站点索引（用于游戏逻辑）")]
        public new int stationIndex
        {
            get { return StationIndex; }
            set { StationIndex = value; Id = value; }
        }

        // 兼容属性，映射到BasePathItem的属性
        [Tooltip("移动到此点的速度倍率（1.0为正常速度）")]
        public new float speedMultiplier
        {
            get { return SpeedMultiplier; }
            set { SpeedMultiplier = value; }
        }

        // 获取路径点的位置
        public Vector2 Position
        {
            get
            {
                return pointTransform != null ? pointTransform.anchoredPosition : Vector2.zero;
            }
        }

        // 构造函数
        public PathItem() { }

        // 带参数的构造函数
        public PathItem(RectTransform transform, bool isStation = false, string name = "", int index = -1, float speedMult = 1.0f)
        {
            pointTransform = transform;
            this.isStation = isStation;
            stationName = name;
            stationIndex = index;
            speedMultiplier = speedMult;
        }

        /// <summary>
        /// 设置路径点高亮状态
        /// </summary>
        /// <param name="highlight">是否高亮</param>
        /// <param name="config">潜艇配置</param>
        public void SetHighlight(bool highlight, SubmarineConfig config)
        {
            ModuleLogManager.LogSubmarine($"SetHighlight: 路径点 {Id} ({stationName}) - 设置高亮状态: {highlight}");

            if (pointImage != null && config != null)
            {
                Color targetColor = highlight ? config.pathHighlightColor : config.pathNormalColor;
                pointImage.color = targetColor;
                ModuleLogManager.LogSubmarine($"SetHighlight: 路径点 {Id} - 颜色已更新为: {targetColor}");
            }
            else
            {
                ModuleLogManager.LogSubmarineWarning($"SetHighlight: 路径点 {Id} - 无法设置高亮状态，Image组件: {(pointImage == null ? "为空" : "有效")}, 配置: {(config == null ? "为空" : "有效")}");
            }
        }

        /// <summary>
        /// 设置路径点为已通过状态
        /// </summary>
        /// <param name="config">潜艇配置</param>
        public void SetPassed(SubmarineConfig config)
        {
            ModuleLogManager.LogSubmarine($"SetPassed: 路径点 {Id} ({stationName}) - 设置为已通过状态");

            if (pointImage != null && config != null)
            {
                pointImage.color = config.pathPassedColor;
                ModuleLogManager.LogSubmarine($"SetPassed: 路径点 {Id} - 颜色已更新为: {config.pathPassedColor}");
            }
            else
            {
                ModuleLogManager.LogSubmarineWarning($"SetPassed: 路径点 {Id} - 无法设置已通过状态，Image组件: {(pointImage == null ? "为空" : "有效")}, 配置: {(config == null ? "为空" : "有效")}");
            }
        }

        /// <summary>
        /// 静态方法：高亮从起点到目标的路径
        /// </summary>
        /// <param name="pathItems">所有路径点</param>
        /// <param name="startStationIndex">起始站点索引</param>
        /// <param name="stepsToMove">移动步数</param>
        /// <param name="config">潜艇配置</param>
        public static void HighlightPathToTarget(List<PathItem> pathItems, int startStationIndex, int stepsToMove, SubmarineConfig config)
        {
            // 记录方法开始和输入参数
            ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 开始高亮路径 - 起始站点索引: {startStationIndex}, 移动步数: {stepsToMove}, 路径点数量: {pathItems?.Count ?? 0}");

            // 参数验证
            if (pathItems == null || pathItems.Count == 0 || config == null)
            {
                ModuleLogManager.LogSubmarineWarning($"HighlightPathToTarget: 参数无效 - 路径点列表: {(pathItems == null ? "为空" : (pathItems.Count == 0 ? "为空列表" : "有效"))}, 配置: {(config == null ? "为空" : "有效")}");
                return;
            }

            // 获取所有站点索引
            List<int> stationIndices = new List<int>();
            int stationCount = 0;
            for (int i = 0; i < pathItems.Count; i++)
            {
                if (pathItems[i].isStation)
                {
                    stationIndices.Add(pathItems[i].stationIndex);
                    stationCount++;
                    //ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 找到站点 - 索引: {i}, 站点索引: {pathItems[i].stationIndex}, 名称: {pathItems[i].stationName}");
                }
            }

            ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 共找到 {stationCount} 个站点，路径点总数: {pathItems.Count}");

            if (stationIndices.Count == 0)
            {
                ModuleLogManager.LogSubmarineWarning("HighlightPathToTarget: 未找到任何站点，无法高亮路径");
                return;
            }

            // 找到当前站点在列表中的位置
            int startIndex = stationIndices.IndexOf(startStationIndex);
            if (startIndex < 0)
            {
                ModuleLogManager.LogSubmarineWarning($"HighlightPathToTarget: 未找到起始站点索引 {startStationIndex}");
                return;
            }

            ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 起始站点在列表中的位置: {startIndex}");

            // 计算目标站点在列表中的位置
            int targetIndex = (startIndex + stepsToMove) % stationIndices.Count;
            if (targetIndex < 0) targetIndex += stationIndices.Count;

            ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 目标站点在列表中的位置: {targetIndex}, 计算公式: ({startIndex} + {stepsToMove}) % {stationIndices.Count} = {targetIndex}");
            ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 起始站点索引: {stationIndices[startIndex]}, 目标站点索引: {stationIndices[targetIndex]}");

            // 高亮路径点
            int highlightedCount = 0;
            for (int i = 0; i < pathItems.Count; i++)
            {
                var pathItem = pathItems[i];
                if (pathItem.isStation)
                {
                    int currentIndex = stationIndices.IndexOf(pathItem.stationIndex);
                    if (currentIndex >= 0)
                    {
                        bool isInPath = IsStationInPath(currentIndex, startIndex, targetIndex);
                        if (isInPath)
                        {
                            pathItem.SetHighlight(isInPath, config);
                            highlightedCount++;
                            //ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 高亮路径点 - 索引: {i}, 站点索引: {pathItem.stationIndex}, 名称: {pathItem.stationName}, 颜色: {config.pathHighlightColor}");
                        }
                        else
                        {
                            //ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 重置路径点 - 索引: {i}, 站点索引: {pathItem.stationIndex}, 名称: {pathItem.stationName}, 颜色: {config.pathNormalColor}");
                        }
                    }
                }
            }

            ModuleLogManager.LogSubmarine($"HighlightPathToTarget: 完成高亮路径 - 共高亮 {highlightedCount} 个路径点");
        }

        /// <summary>
        /// 判断站点是否在路径上
        /// </summary>
        private static bool IsStationInPath(int stationIndex, int startIndex, int targetIndex)
        {
            bool result;

            if (targetIndex >= startIndex)
            {
                // 正向路径（不跨越列表边界）
                result = stationIndex >= startIndex && stationIndex <= targetIndex;
                ModuleLogManager.LogSubmarine($"IsStationInPath: 正向路径 - 站点索引: {stationIndex}, 起始索引: {startIndex}, 目标索引: {targetIndex}, 结果: {result}, 判断条件: {stationIndex} >= {startIndex} && {stationIndex} <= {targetIndex}");
            }
            else
            {
                // 反向路径（跨越列表边界）
                result = stationIndex >= startIndex || stationIndex <= targetIndex;
                ModuleLogManager.LogSubmarine($"IsStationInPath: 反向路径 - 站点索引: {stationIndex}, 起始索引: {startIndex}, 目标索引: {targetIndex}, 结果: {result}, 判断条件: {stationIndex} >= {startIndex} || {stationIndex} <= {targetIndex}");
            }

            return result;
        }
        public void PlayWhenPass()
        {
            //用Dotween实现图片的Alpha值还原到0
            if (pointImage != null)
            {
                // 取消之前的动画
                DOTween.Kill(pointImage);

                // 将Alpha值从当前值渐变到1
                DOTween.To(() => pointImage.color.a, x =>
                {
                    Color newColor = pointImage.color;
                    newColor.a = x;
                    pointImage.color = newColor;
                }, 0f, 0.5f)
                    .SetEase(DG.Tweening.Ease.OutQuad)
                    .SetTarget(pointImage);
            }
        }

        /// <summary>
        /// 播放闪烁效果
        /// </summary>
        public void PlayShine()
        {
            //用Dotween实现图片的Alpha值快速的变化
            if (pointImage != null)
            {
                // 取消之前的动画
                DOTween.Kill(pointImage);

                // 创建一个闪烁序列
                DG.Tweening.Sequence sequence = DOTween.Sequence();

                // 保存初始Alpha值
                float initialAlpha = 0;

                // 添加闪烁效果（淡入淡出循环）
                sequence.Append(
                    DOTween.To(() => pointImage.color.a, x =>
                    {
                        Color newColor = pointImage.color;
                        newColor.a = x;
                        pointImage.color = newColor;
                    }, 1f, 0.3f)
                );

                sequence.Append(
                    DOTween.To(() => pointImage.color.a, x =>
                    {
                        Color newColor = pointImage.color;
                        newColor.a = x;
                        pointImage.color = newColor;
                    }, 0.3f, 0.3f)
                );

                sequence.Append(
                    DOTween.To(() => pointImage.color.a, x =>
                    {
                        Color newColor = pointImage.color;
                        newColor.a = x;
                        pointImage.color = newColor;
                    }, 1f, 0.3f)
                );

                sequence.Append(
                    DOTween.To(() => pointImage.color.a, x =>
                    {
                        Color newColor = pointImage.color;
                        newColor.a = x;
                        pointImage.color = newColor;
                    }, 0.3f, 0.3f)
                );

                sequence.Append(
                    DOTween.To(() => pointImage.color.a, x =>
                    {
                        Color newColor = pointImage.color;
                        newColor.a = x;
                        pointImage.color = newColor;
                    }, initialAlpha, 0.3f)
                );

                // 设置目标，便于后续取消
                sequence.SetTarget(pointImage);
            }
        }

#if UNITY_EDITOR
        [Button("获取RectTransform组件")]
        private void GetBind()
        {
            pointTransform = GetComponent<RectTransform>();
            pointImage = GetComponent<Image>();
            StationName = $"{PointType.ToString()}_{Id}";
            LogF8.Log($"已获取 {gameObject.name} 的 RectTransform 组件");
        }
#endif

        // 重写OnEnable方法，确保在启用时设置OnReachAction
        private void OnEnable()
        {
            // 设置到达事件，当PathKit触发TriggerReachAction时，调用PlayWhenPass
            OnReachAction = PlayWhenPass;
        }
    }
}
