using UnityEngine;
using QFramework;
using QHLC.Controllers;
using QHLC.Views;
using F8Framework.Core;
using QHLC.FSM.HLight;
using QHLC.FSM.Wheel;
using Sirenix.OdinInspector;

namespace QHLC.FSM
{
    /// <summary>
    /// HFSM测试场景设置脚本
    /// 用于快速配置和初始化测试环境
    /// </summary>
    public class HFSMTestSceneSetup : MonoBehaviour
    {
        #region 序列化字段
        [Header("自动创建组件")]
        [SerializeField] private bool autoCreateComponents = true;
        [SerializeField] private bool autoInitialize = true;
        
        [Header("测试配置")]
        [SerializeField] private bool enableAutoTest = false;
        [SerializeField] private float autoTestInterval = 3f;
        
        [Header("预制体引用（可选）")]
        [SerializeField] private GameObject gameControllerPrefab;
        [SerializeField] private GameObject wheelViewPrefab;
        [SerializeField] private GameObject lightViewPrefab;
        #endregion

        #region 私有字段
        private GameObject gameControllerInstance;
        private GameObject wheelViewInstance;
        private GameObject lightViewInstance;
        private HFSMTestController testController;
        #endregion

        #region Unity生命周期
        private void Start()
        {
            if (autoCreateComponents)
            {
                SetupTestScene();
            }
        }
        #endregion

        #region 场景设置
        /// <summary>
        /// 设置测试场景
        /// </summary>
        [Button("设置测试场景")]
        public void SetupTestScene()
        {
            LogF8.Log("HFSMTestSceneSetup: 开始设置测试场景");

            // 1. 创建或查找GameController
            SetupGameController();

            // 2. 创建或查找WheelView
            SetupWheelView();

            // 3. 创建或查找LightView
            SetupLightView();

            // 4. 创建或查找GameFSMCoordinator
            SetupGameFSMCoordinator();

            // 5. 创建HFSMTestController
            SetupTestController();

            // 6. 如果启用自动初始化，则初始化所有组件
            if (autoInitialize)
            {
                InitializeComponents();
            }

            LogF8.Log("HFSMTestSceneSetup: 测试场景设置完成");
        }

        /// <summary>
        /// 设置GameController
        /// </summary>
        private void SetupGameController()
        {
            var existingController = FindObjectOfType<GameController>();
            if (existingController != null)
            {
                gameControllerInstance = existingController.gameObject;
                LogF8.Log("HFSMTestSceneSetup: 找到现有的GameController");
                return;
            }

            if (gameControllerPrefab != null)
            {
                gameControllerInstance = Instantiate(gameControllerPrefab);
                gameControllerInstance.name = "GameController";
            }
            else
            {
                // 创建空的GameObject并添加GameController组件
                gameControllerInstance = new GameObject("GameController");
                gameControllerInstance.AddComponent<GameController>();
            }

            LogF8.Log("HFSMTestSceneSetup: 创建了新的GameController");
        }

        /// <summary>
        /// 设置WheelView
        /// </summary>
        private void SetupWheelView()
        {
            var existingWheelView = FindObjectOfType<WheelView>();
            if (existingWheelView != null)
            {
                wheelViewInstance = existingWheelView.gameObject;
                LogF8.Log("HFSMTestSceneSetup: 找到现有的WheelView");
                return;
            }

            if (wheelViewPrefab != null)
            {
                wheelViewInstance = Instantiate(wheelViewPrefab);
                wheelViewInstance.name = "WheelView";
            }
            else
            {
                // 创建空的GameObject并添加WheelView组件
                wheelViewInstance = new GameObject("WheelView");
                wheelViewInstance.AddComponent<WheelView>();
                
                // 添加WheelFSM组件
                wheelViewInstance.AddComponent<WheelFSM>();
            }

            LogF8.Log("HFSMTestSceneSetup: 创建了新的WheelView");
        }

        /// <summary>
        /// 设置LightView
        /// </summary>
        private void SetupLightView()
        {
            var existingLightView = FindObjectOfType<LightView>();
            if (existingLightView != null)
            {
                lightViewInstance = existingLightView.gameObject;
                LogF8.Log("HFSMTestSceneSetup: 找到现有的LightView");
                return;
            }

            if (lightViewPrefab != null)
            {
                lightViewInstance = Instantiate(lightViewPrefab);
                lightViewInstance.name = "LightView";
            }
            else
            {
                // 创建空的GameObject并添加LightView组件
                lightViewInstance = new GameObject("LightView");
                lightViewInstance.AddComponent<LightView>();
                
                // 添加LightFSM组件
                lightViewInstance.AddComponent<LightHFSM>();
            }

            LogF8.Log("HFSMTestSceneSetup: 创建了新的LightView");
        }

        /// <summary>
        /// 设置GameFSMCoordinator
        /// </summary>
        private void SetupGameFSMCoordinator()
        {
            var existingCoordinator = FindObjectOfType<GameFSMCoordinator>();
            if (existingCoordinator != null)
            {
                LogF8.Log("HFSMTestSceneSetup: 找到现有的GameFSMCoordinator");
                return;
            }

            // 在GameController上添加GameFSMCoordinator组件
            if (gameControllerInstance != null)
            {
                var coordinator = gameControllerInstance.GetComponent<GameFSMCoordinator>();
                if (coordinator == null)
                {
                    gameControllerInstance.AddComponent<GameFSMCoordinator>();
                }
                LogF8.Log("HFSMTestSceneSetup: 在GameController上添加了GameFSMCoordinator");
            }
        }

        /// <summary>
        /// 设置测试控制器
        /// </summary>
        private void SetupTestController()
        {
            var existingTestController = FindObjectOfType<HFSMTestController>();
            if (existingTestController != null)
            {
                testController = existingTestController;
                LogF8.Log("HFSMTestSceneSetup: 找到现有的HFSMTestController");
                return;
            }

            // 创建测试控制器
            var testControllerObject = new GameObject("HFSMTestController");
            testController = testControllerObject.AddComponent<HFSMTestController>();

            // 配置测试控制器
            ConfigureTestController();

            LogF8.Log("HFSMTestSceneSetup: 创建了新的HFSMTestController");
        }

        /// <summary>
        /// 配置测试控制器
        /// </summary>
        private void ConfigureTestController()
        {
            if (testController == null) return;

            // 使用反射设置私有字段（仅用于测试）
            var testControllerType = typeof(HFSMTestController);
            
            // 设置autoTest字段
            var autoTestField = testControllerType.GetField("autoTest", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (autoTestField != null)
            {
                autoTestField.SetValue(testController, enableAutoTest);
            }

            // 设置autoTestInterval字段
            var intervalField = testControllerType.GetField("autoTestInterval", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (intervalField != null)
            {
                intervalField.SetValue(testController, autoTestInterval);
            }

            LogF8.Log($"HFSMTestSceneSetup: 配置测试控制器 - 自动测试: {enableAutoTest}, 间隔: {autoTestInterval}秒");
        }

        /// <summary>
        /// 初始化所有组件
        /// </summary>
        private void InitializeComponents()
        {
            LogF8.Log("HFSMTestSceneSetup: 开始初始化所有组件");

            // 初始化GameController
            if (gameControllerInstance != null)
            {
                var gameController = gameControllerInstance.GetComponent<GameController>();
                if (gameController != null)
                {
                    // GameController会在其Start方法中自动初始化
                    LogF8.Log("HFSMTestSceneSetup: GameController将自动初始化");
                }
            }

            // 初始化测试控制器
            if (testController != null)
            {
                // HFSMTestController会在其Start方法中自动初始化
                LogF8.Log("HFSMTestSceneSetup: HFSMTestController将自动初始化");
            }

            LogF8.Log("HFSMTestSceneSetup: 组件初始化设置完成");
        }
        #endregion

        #region 手动操作
        /// <summary>
        /// 清理测试场景
        /// </summary>
        [Button("清理测试场景")]
        public void CleanupTestScene()
        {
            LogF8.Log("HFSMTestSceneSetup: 开始清理测试场景");

            // 销毁创建的实例
            if (gameControllerInstance != null && gameControllerPrefab != null)
            {
                DestroyImmediate(gameControllerInstance);
                gameControllerInstance = null;
            }

            if (wheelViewInstance != null && wheelViewPrefab != null)
            {
                DestroyImmediate(wheelViewInstance);
                wheelViewInstance = null;
            }

            if (lightViewInstance != null && lightViewPrefab != null)
            {
                DestroyImmediate(lightViewInstance);
                lightViewInstance = null;
            }

            if (testController != null)
            {
                DestroyImmediate(testController.gameObject);
                testController = null;
            }

            LogF8.Log("HFSMTestSceneSetup: 测试场景清理完成");
        }

        /// <summary>
        /// 重新设置测试场景
        /// </summary>
        [Button("重新设置测试场景")]
        public void ResetTestScene()
        {
            CleanupTestScene();
            SetupTestScene();
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 验证场景设置
        /// </summary>
        [Button("验证场景设置")]
        public void ValidateSceneSetup()
        {
            LogF8.Log("=== HFSM测试场景验证 ===");

            var gameController = FindObjectOfType<GameController>();
            LogF8.Log($"GameController: {(gameController != null ? "✓" : "✗")}");

            var wheelView = FindObjectOfType<WheelView>();
            LogF8.Log($"WheelView: {(wheelView != null ? "✓" : "✗")}");

            var lightView = FindObjectOfType<LightView>();
            LogF8.Log($"LightView: {(lightView != null ? "✓" : "✗")}");

            var gameFSMCoordinator = FindObjectOfType<GameFSMCoordinator>();
            LogF8.Log($"GameFSMCoordinator: {(gameFSMCoordinator != null ? "✓" : "✗")}");

            var hfsmTestController = FindObjectOfType<HFSMTestController>();
            LogF8.Log($"HFSMTestController: {(hfsmTestController != null ? "✓" : "✗")}");

            LogF8.Log("========================");
        }
        #endregion
    }
} 