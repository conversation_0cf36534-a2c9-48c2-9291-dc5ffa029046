using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QHLC.Models;
using QFramework;
using QHLC.Views;
using F8Framework.Core;
using QHLC.Events;
using QHLC.Common;
using QHLC.Utilities;

namespace QHLC
{
    /// <summary>
    /// 潜艇系统，负责处理潜艇相关的业务逻辑
    /// </summary>
    public class SubmarineSystem : AbstractSystem, ICanGetModel, ICanGetUtility
    {
        private SubmarineModel model;
        private ConfigurationService configService;
        private SubmarineErrorHandler errorHandler;

        protected override void OnInit()
        {
            model = this.GetModel<SubmarineModel>();
            configService = this.GetUtility<ConfigurationService>();
            errorHandler = this.GetUtility<SubmarineErrorHandler>();
        }

        /// <summary>
        /// 停止潜艇移动
        /// </summary>
        public void StopMovement()
        {
            if (model.IsMoving.Value)
            {
                model.IsMoving.Value = false;
                TypeEventSystem.Global.Send(new SubmarineMoveCompletedEvent());
            }
        }

        /// <summary>
        /// 处理潜艇移动完成
        /// </summary>
        public void OnMovementCompleted(int newStationIndex)
        {
            try
            {
                // 验证新站点索引
                if (newStationIndex < 0 || newStationIndex >= model.GetStationCount())
                {
                    throw new SubmarineException(
                        SubmarineErrorType.InvalidStation,
                        $"无效的站点索引: {newStationIndex}"
                    );
                }

                // 更新状态
                model.CurrentStationIndex.Value = newStationIndex;
                model.IsMoving.Value = false;

                // 发送移动完成事件
                TypeEventSystem.Global.Send(new SubmarineMoveCompletedEvent());
            }
            catch (SubmarineException ex)
            {
                errorHandler.HandleError(ex);
            }
        }

        /// <summary>
        /// 重置潜艇状态
        /// </summary>
        public void ResetSubmarine()
        {
            model.Reset();
            TypeEventSystem.Global.Send(new SubmarineResetEvent());
        }

        /// <summary>
        /// 初始化路径点数据
        /// </summary>
        public void InitializePathItems(List<PathItem> pathItems)
        {
            try
            {
                if (pathItems == null || pathItems.Count == 0)
                {
                    throw new SubmarineException(
                        SubmarineErrorType.InvalidPath,
                        "路径点数据为空"
                    );
                }

                // 直接使用PathItem列表，不再需要转换为PathItemData
                model.SetPathItems(pathItems);
                
                // 发送路径点初始化完成事件
                TypeEventSystem.Global.Send(new SubmarinePathInitializedEvent { PathItems = pathItems });
                
                ModuleLogManager.LogSubmarine($"SubmarineSystem.InitializePathItems: 路径点初始化完成，数量: {pathItems.Count}，已发送初始化事件");
            }
            catch (SubmarineException ex)
            {
                errorHandler.HandleError(ex);
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfiguration()
        {
            try
            {
                configService.ReloadConfig<ScriptableObjects.SubmarineConfig>();

                TypeEventSystem.Global.Send(new SubmarineConfigReloadedEvent
                {
                    Success = true,
                    Message = "配置重载成功"
                });
            }
            catch (SubmarineException ex)
            {
                errorHandler.HandleError(ex);

                TypeEventSystem.Global.Send(new SubmarineConfigReloadedEvent
                {
                    Success = false,
                    Message = ex.Message
                });
            }
        }

        /// <summary>
        /// 重置动画状态
        /// </summary>
        public void ResetAnimation()
        {
            TypeEventSystem.Global.Send(new SubmarineResetEvent());
        }

        /// <summary>
        /// 设置潜艇移动状态
        /// </summary>
        /// <param name="isMoving">是否正在移动</param>
        public void SetMovingState(bool isMoving)
        {
            try
            {
                model.IsMoving.Value = isMoving;
                ModuleLogManager.LogSubmarine($"SubmarineSystem.SetMovingState: 设置移动状态为 {isMoving}");
            }
            catch (SubmarineException ex)
            {
                errorHandler.HandleError(ex);
            }
        }

        /// <summary>
        /// 更新潜艇方向
        /// </summary>
        /// <param name="direction">新的方向</param>
        public void UpdateDirection(SubmarineDirection direction)
        {
            try
            {
                model.CurrentDirection.Value = direction;
                
                // 发送方向改变事件
                TypeEventSystem.Global.Send(new SubmarineDirectionChangedEvent { Direction = direction });
                
                ModuleLogManager.LogSubmarine($"SubmarineSystem.UpdateDirection: 更新方向为 {direction}");
            }
            catch (SubmarineException ex)
            {
                errorHandler.HandleError(ex);
            }
        }
    }
}
