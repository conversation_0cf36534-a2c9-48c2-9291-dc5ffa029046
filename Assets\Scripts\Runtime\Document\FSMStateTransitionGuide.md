# FSM状态切换指南

## 概述

本文档详细描述了QHLC项目中所有状态机（FSM）的状态定义、切换条件和协调逻辑。项目采用分层状态机架构，包含一个主游戏状态机协调器和四个独立的子状态机。

**重要更新：GameFSMCoordinator现已改为单例模式，所有状态切换都必须通过GameFSMCoordinator的统一接口进行。**

## 架构概览

### 状态机层次结构
```
GameFSMCoordinator (主协调器 - 单例模式)
├── LightHFSM (灯光状态机)
├── WheelFSM (转盘状态机)
├── SubmarineFSM (潜艇状态机)
└── GameStateType (游戏状态枚举)
```

### 通信机制
- 使用 `FSMStateChangedEvent` 事件进行状态机间通信
- `GameFSMCoordinator` 单例负责协调各个独立FSM的状态切换
- **所有状态切换必须通过GameFSMCoordinator的公共接口进行**
- 每个FSM都可以独立运行，但通过事件系统保持同步

### 单例模式设计

#### GameFSMCoordinator单例
```csharp
public class GameFSMCoordinator : MonoBehaviour
{
    private static GameFSMCoordinator _instance;
    public static GameFSMCoordinator Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<GameFSMCoordinator>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("GameFSMCoordinator");
                    _instance = go.AddComponent<GameFSMCoordinator>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }
}
```

#### 公共接口方法
```csharp
// 请求切换游戏状态
public void RequestStateChange(GameStateType targetState)

// 请求切换灯光状态
public void RequestLightStateChange(LightStateType targetState)

// 请求切换转盘状态
public void RequestWheelStateChange(WheelStateType targetState)

// 请求切换潜艇状态
public void RequestSubmarineStateChange(SubmarineStateType targetState)
```

## 状态切换规范

### 1. 禁止直接调用FSM的ChangeState方法

**错误做法：**
```csharp
// ❌ 禁止直接调用
mFSM.ChangeState(WheelStateType.Spinning);
submarineFSM.ChangeState(SubmarineStateType.Moving);
```

**正确做法：**
```csharp
// ✅ 通过GameFSMCoordinator单例请求状态切换
if (GameFSMCoordinator.Instance != null)
{
    GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Spinning);
    GameFSMCoordinator.Instance.RequestSubmarineStateChange(SubmarineStateType.Moving);
}
```

### 2. 状态切换逻辑统一在CoordinateOtherFSMs中处理

所有状态切换后的协调逻辑都在`GameFSMCoordinator.CoordinateOtherFSMs`方法中统一处理：

```csharp
private void CoordinateOtherFSMs(GameStateType previousState, GameStateType currentState)
{
    // 所有状态切换逻辑都在这里统一处理
    switch (previousState, currentState)
    {
        case (GameStateType.Idle, GameStateType.FlashingLights):
            // 确保WheelFSM处于闲置状态
            break;
        case (GameStateType.WheelSpinning, GameStateType.SubmarineMoving):
            // 切换SubmarineFSM到移动状态
            break;
        // ... 其他状态切换逻辑
    }
}
```

### 3. 错误处理和降级机制

每个状态切换请求都包含降级机制：

```csharp
// 通过GameFSMCoordinator请求状态切换
if (GameFSMCoordinator.Instance != null)
{
    GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Spinning);
}
else
{
    // 降级机制：如果单例未找到，直接切换状态
    ModuleLogManager.LogWheel("GameFSMCoordinator单例未找到，直接切换状态");
    mFSM.ChangeState(WheelStateType.Spinning);
}
```

## 状态定义

### 1. 游戏状态 (GameStateType)

**文件位置**: `Assets/Scripts/Runtime/FSM/GameStateType.cs`

```csharp
public enum GameStateType
{
    Idle,           // 闲置状态 - 游戏等待用户输入
    FlashingLights, // 灯光闪烁状态 - 灯光激活阶段
    WheelSpinning,  // 转盘旋转状态 - 转盘旋转阶段
    SubmarineMoving // 潜艇移动状态 - 潜艇移动阶段
}
```

### 2. 灯光状态 (LightStateType)

**文件位置**: `Assets/Scripts/Runtime/FSM/Light/HLight/LightHFSM.cs`

```csharp
public enum LightStateType
{
    Idle,       // 闲置状态 - 等待用户点击
    Activating, // 激活中状态 - 正在点亮灯光
    Activated   // 已激活状态 - 所有灯光已点亮，播放动画
}
```

### 3. 转盘状态 (WheelStateType)

**文件位置**: `Assets/Scripts/Runtime/FSM/Wheel/WheelStateType.cs`

```csharp
public enum WheelStateType
{
    Idle,     // 闲置状态 - 缓慢旋转，包含嵌套的LightFSM
    Spinning, // 旋转状态 - 快速旋转
    Flashing  // 闪烁状态 - 停止旋转，闪烁指示结果
}
```

### 4. 潜艇状态 (SubmarineStateType)

**文件位置**: `Assets/Scripts/Runtime/FSM/Submarine/SubmarineStateType.cs`

```csharp
public enum SubmarineStateType
{
    Idle,    // 闲置状态 - 播放闲置动画
    Moving,  // 移动状态 - 移动到目标位置
    Arrived  // 到达状态 - 播放到达效果
}
```

## 状态切换流程

### 完整游戏流程

```mermaid
graph TD
    A[游戏开始] --> B[所有FSM初始化为Idle状态]
    B --> C[用户点击灯光]
    C --> D[LightFSM通过GameFSMCoordinator请求: Idle → Activating]
    D --> E{所有灯光是否点亮?}
    E -->|否| C
    E -->|是| F[LightFSM: Activating → Activated]
    F --> G[GameFSMCoordinator: Idle → WheelSpinning]
    G --> H[CoordinateOtherFSMs: WheelFSM → Spinning]
    H --> I[转盘旋转完成]
    I --> J[WheelFSM通过GameFSMCoordinator请求: Spinning → Flashing]
    J --> K[GameFSMCoordinator: WheelSpinning → SubmarineMoving]
    K --> L[CoordinateOtherFSMs: SubmarineFSM → Moving]
    L --> M[潜艇移动完成]
    M --> N[SubmarineFSM通过GameFSMCoordinator请求: Moving → Arrived]
    N --> O[GameFSMCoordinator: SubmarineMoving → Idle]
    O --> P[CoordinateOtherFSMs: 所有FSM重置为Idle状态]
    P --> B
```

### 详细状态切换条件

#### 1. 灯光状态机 (LightHFSM)

**Idle → Activating**
- **触发条件**: 检测到缓存的用户输入事件
- **执行逻辑**: 
  ```csharp
  if (inputBufferSystem.HasBufferedInputs())
  {
      inputBufferSystem.ProcessNextInput();
      mFSM.ChangeState(LightStateType.Activating);
      
      // 通过GameFSMCoordinator请求游戏状态切换
      if (GameFSMCoordinator.Instance != null)
      {
          GameFSMCoordinator.Instance.RequestStateChange(GameStateType.FlashingLights);
      }
  }
  ```

**Activating → Activated**
- **触发条件**: 所有灯光都已激活
- **执行逻辑**:
  ```csharp
  if (lightModel.AllLightsActivated.Value)
  {
      TypeEventSystem.Global.Send(new AllLightsActivatedEvent());
      mFSM.ChangeState(LightStateType.Activated);
  }
  ```

**Activated → Idle**
- **触发条件**: 灯光动画播放完成
- **执行逻辑**:
  ```csharp
  private void OnLightAnimationCompleted(LightAnimationCompletedEvent evt)
  {
      lightModel.ResetLights(true);
      mFSM.ChangeState(LightStateType.Idle);
  }
  ```

#### 2. 转盘状态机 (WheelFSM)

**Idle → Spinning**
- **触发条件**: 通过GameFSMCoordinator的协调指令
- **执行逻辑**: 
  ```csharp
  // 在WheelIdleHState中检测到缓存次数时
  if (GameFSMCoordinator.Instance != null)
  {
      GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Spinning);
  }
  ```

**Spinning → Flashing**
- **触发条件**: 转盘旋转完成
- **执行逻辑**:
  ```csharp
  if (wheelModel.SpinningCompleted.Value)
  {
      if (GameFSMCoordinator.Instance != null)
      {
          GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Flashing);
      }
  }
  ```

**Flashing → Idle**
- **触发条件**: 闪烁完成且潜艇移动完成
- **执行逻辑**:
  ```csharp
  if (wheelModel.FlashingCompleted.Value)
  {
      if (GameFSMCoordinator.Instance != null)
      {
          GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Idle);
      }
  }
  ```

#### 3. 潜艇状态机 (SubmarineFSM)

**Idle → Moving**
- **触发条件**: 通过GameFSMCoordinator的协调指令
- **执行逻辑**: 由GameFSMCoordinator.CoordinateOtherFSMs方法协调切换

**Moving → Arrived**
- **触发条件**: 潜艇移动完成
- **执行逻辑**:
  ```csharp
  // 在移动完成回调中
  if (GameFSMCoordinator.Instance != null)
  {
      GameFSMCoordinator.Instance.RequestSubmarineStateChange(SubmarineStateType.Arrived);
  }
  ```

**Arrived → Idle**
- **触发条件**: 到达状态显示时间结束
- **执行逻辑**:
  ```csharp
  if (arrivalTimer >= ARRIVAL_DISPLAY_TIME)
  {
      if (GameFSMCoordinator.Instance != null)
      {
          GameFSMCoordinator.Instance.RequestSubmarineStateChange(SubmarineStateType.Idle);
      }
  }
  ```

#### 4. 游戏协调器 (GameFSMCoordinator)

**状态协调逻辑**:

```csharp
private void CoordinateOtherFSMs(GameStateType previousState, GameStateType currentState)
{
    // 所有状态切换逻辑都在这里统一处理
    
    // 闲置 → 灯光闪烁：确保WheelFSM处于闲置状态
    if (previousState == GameStateType.Idle && currentState == GameStateType.FlashingLights)
    {
        if (wheelFSM.CurrentStateId != WheelStateType.Idle)
        {
            wheelFSM.ChangeState(WheelStateType.Idle);
        }
    }
    
    // 闲置 → 转盘旋转：切换WheelFSM到旋转状态
    else if (previousState == GameStateType.Idle && currentState == GameStateType.WheelSpinning)
    {
        wheelFSM.ChangeState(WheelStateType.Spinning);
    }
    
    // 转盘旋转 → 潜艇移动：切换SubmarineFSM到移动状态
    else if (previousState == GameStateType.WheelSpinning && currentState == GameStateType.SubmarineMoving)
    {
        submarineFSM.ChangeState(SubmarineStateType.Moving);
        StartCoroutine(SendMoveSubmarineEventDelayed());
    }
    
    // 潜艇移动 → 闲置：切换所有FSM到闲置状态
    else if (previousState == GameStateType.SubmarineMoving && currentState == GameStateType.Idle)
    {
        if (submarineFSM.CurrentStateId != SubmarineStateType.Idle)
        {
            submarineFSM.ChangeState(SubmarineStateType.Idle);
        }
        if (wheelFSM.CurrentStateId != WheelStateType.Idle)
        {
            wheelFSM.ChangeState(WheelStateType.Idle);
        }
    }
}
```

## 事件系统

### FSM状态变化事件

**事件结构**:
```csharp
public struct FSMStateChangedEvent
{
    public FSMType FSMType;        // FSM类型
    public string PreviousState;   // 前一个状态
    public string CurrentState;    // 当前状态
}
```

**FSM类型枚举**:
```csharp
public enum FSMType
{
    Game,      // 主游戏FSM
    Light,     // 灯光FSM
    Wheel,     // 转盘FSM
    Submarine  // 潜艇FSM
}
```

### 关键游戏事件

1. **AllLightsActivatedEvent** - 所有灯光激活完成
2. **WheelStopCompletedEvent** - 转盘停止完成
3. **WheelFlashingCompletedEvent** - 转盘闪烁完成
4. **SubmarineMoveCompletedEvent** - 潜艇移动完成
5. **LightAnimationCompletedEvent** - 灯光动画完成

## 测试和调试

### 测试脚本

使用`GameFSMCoordinatorTest`脚本来测试状态切换机制：

```csharp
// 测试游戏状态切换
GameFSMCoordinator.Instance.RequestStateChange(GameStateType.FlashingLights);

// 测试转盘状态切换
GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Spinning);

// 测试潜艇状态切换
GameFSMCoordinator.Instance.RequestSubmarineStateChange(SubmarineStateType.Moving);
```

### 调试日志

每个状态切换都会产生详细的日志：

```
[时间] [模块] [方法名]: 详细信息
例如：
GameFSMCoordinator.CoordinateOtherFSMs: 协调状态切换 - 从 Idle 到 FlashingLights
WheelIdleHState: 成功驱动转盘旋转，通过GameFSMCoordinator请求切换到旋转状态
```

## 最佳实践

### 1. 状态切换请求
- 始终通过GameFSMCoordinator的公共接口请求状态切换
- 包含错误处理和降级机制
- 添加详细的日志记录

### 2. 状态协调逻辑
- 所有状态协调逻辑都在CoordinateOtherFSMs方法中实现
- 避免在各个FSM中直接调用其他FSM的方法
- 保持状态切换的原子性和一致性

### 3. 错误处理
- 检查GameFSMCoordinator单例是否可用
- 提供降级机制以防单例失效
- 记录所有异常情况

### 4. 性能考虑
- 避免频繁的状态切换
- 使用状态检查避免重复切换
- 合理使用协程处理延迟操作

## 嵌套状态机

### WheelFSM中的LightFSM嵌套

转盘的Idle状态包含一个嵌套的LightFSM：

```csharp
public class WheelIdleHState : AbstractHState<WheelStateType, WheelView>
{
    private LightHFSM lightHFSM;
    
    protected override void OnEnter()
    {
        // 启动嵌套的Light状态机
        if (lightHFSM != null)
        {
            lightHFSM.StartFSM();
        }
    }
    
    public void ActivateLight()
    {
        if (lightHFSM != null)
        {
            // 通过GameFSMCoordinator请求灯光状态切换
            if (GameFSMCoordinator.Instance != null)
            {
                GameFSMCoordinator.Instance.RequestLightStateChange(LightStateType.Activating);
            }
        }
    }
}
```

这种设计允许：
- 转盘在闲置状态时，灯光系统可以独立运行
- 灯光状态的变化会通过GameFSMCoordinator统一协调
- 保持了各个系统的独立性和可维护性
- 确保状态切换的一致性和可追踪性

## 日志系统

### 日志级别

每个状态机都使用专门的日志方法：
- `ModuleLogManager.LogLight()` - 灯光FSM日志
- `ModuleLogManager.LogWheel()` - 转盘FSM日志
- `ModuleLogManager.LogSubmarine()` - 潜艇FSM日志
- `LogF8.Log()` - GameFSMCoordinator日志

### 日志格式

```
[时间] [模块] [方法名]: 详细信息
例如：
GameFSMCoordinator.CoordinateOtherFSMs: 协调状态切换 - 从 Idle 到 FlashingLights
WheelIdleHState: 成功驱动转盘旋转，通过GameFSMCoordinator请求切换到旋转状态
```

## 总结

通过将GameFSMCoordinator改为单例模式并统一所有状态切换逻辑，我们实现了：

1. **统一的状态管理**：所有状态切换都通过GameFSMCoordinator进行
2. **更好的可维护性**：状态切换逻辑集中在CoordinateOtherFSMs方法中
3. **更强的一致性**：避免了各个FSM之间的直接依赖
4. **更好的调试能力**：统一的日志和错误处理
5. **更高的可靠性**：包含降级机制和错误处理

这种设计确保了游戏状态机系统的稳定性和可扩展性。 