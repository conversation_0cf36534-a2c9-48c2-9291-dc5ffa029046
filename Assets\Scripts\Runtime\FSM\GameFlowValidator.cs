using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;
using QHLC.Events;
using QHLC.FSM.Wheel;
using QHLC.FSM.HLight;
using F8Framework.Core;
using Sirenix.OdinInspector;
using QHLC.Views;

namespace QHLC.FSM
{
    /// <summary>
    /// 游戏流程验证器，用于验证修复后的游戏流程是否正确
    /// </summary>
    public class GameFlowValidator : MonoBehaviour
    {
        [Header("验证配置")]
        [SerializeField] private bool enableValidation = true;
        [SerializeField] private bool logDetailedFlow = true;

        [Header("流程状态")]
        [ShowInInspector, ReadOnly]
        private int inputCount = 0;
        [ShowInInspector, ReadOnly]
        private int lightsActivated = 0;
        [ShowInInspector, ReadOnly]
        private int wheelSpinCount = 0;
        [ShowInInspector, ReadOnly]
        private int submarineMovements = 0;

        [Header("验证结果")]
        [ShowInInspector, ReadOnly]
        private bool flowValidationPassed = true;
        [ShowInInspector, ReadOnly]
        private List<string> validationErrors = new List<string>();

        private void Awake()
        {
            if (!enableValidation) return;

            // 注册所有相关事件
            TypeEventSystem.Global.Register<LightActivatedEvent>(OnLightActivated);
            TypeEventSystem.Global.Register<AllLightsActivatedEvent>(OnAllLightsActivated);
            TypeEventSystem.Global.Register<LightAnimationCompletedEvent>(OnLightAnimationCompleted);
            TypeEventSystem.Global.Register<WheelBufferCountChangedEvent>(OnWheelBufferCountChanged);
            TypeEventSystem.Global.Register<WheelSpinningStartEvent>(OnWheelSpinningStart);
            TypeEventSystem.Global.Register<SubmarineMovementStartEvent>(OnSubmarineMovementStart);
            TypeEventSystem.Global.Register<SubmarineMoveCompletedEvent>(OnSubmarineMoveCompleted);
        }

        private void OnDestroy()
        {
            if (!enableValidation) return;

            // 注销所有事件
            TypeEventSystem.Global.UnRegister<LightActivatedEvent>(OnLightActivated);
            TypeEventSystem.Global.UnRegister<AllLightsActivatedEvent>(OnAllLightsActivated);
            TypeEventSystem.Global.UnRegister<LightAnimationCompletedEvent>(OnLightAnimationCompleted);
            TypeEventSystem.Global.UnRegister<WheelBufferCountChangedEvent>(OnWheelBufferCountChanged);
            TypeEventSystem.Global.UnRegister<WheelSpinningStartEvent>(OnWheelSpinningStart);
            TypeEventSystem.Global.UnRegister<SubmarineMovementStartEvent>(OnSubmarineMovementStart);
            TypeEventSystem.Global.UnRegister<SubmarineMoveCompletedEvent>(OnSubmarineMoveCompleted);
        }

        #region 事件处理

        private void OnLightActivated(LightActivatedEvent evt)
        {
            lightsActivated++;
            if (logDetailedFlow)
            {
                LogF8.Log($"[GameFlowValidator] 灯光激活 - 索引: {evt.LightIndex}, 总计: {lightsActivated}");
            }
        }

        private void OnAllLightsActivated(AllLightsActivatedEvent evt)
        {
            if (logDetailedFlow)
            {
                LogF8.Log($"[GameFlowValidator] 所有灯光激活完成");
            }
        }

        private void OnLightAnimationCompleted(LightAnimationCompletedEvent evt)
        {
            if (logDetailedFlow)
            {
                LogF8.Log($"[GameFlowValidator] 灯光动画完成 - 应该增加转盘缓存");
            }
        }

        private void OnWheelBufferCountChanged(WheelBufferCountChangedEvent evt)
        {
            if (logDetailedFlow)
            {
                LogF8.Log($"[GameFlowValidator] 转盘缓存变化 - 当前缓存: {evt.Count}");
            }

            // 验证：灯光动画完成后应该增加转盘缓存
            if (evt.Count > 0 && lightsActivated >= 3)
            {
                LogF8.Log($"[GameFlowValidator] ✅ 验证通过：灯光激活后正确增加了转盘缓存");
            }
        }

        private void OnWheelSpinningStart(WheelSpinningStartEvent evt)
        {
            wheelSpinCount++;
            if (logDetailedFlow)
            {
                LogF8.Log($"[GameFlowValidator] 转盘开始旋转 - 目标结果: {evt.TargetResult}");
            }
        }

        private void OnSubmarineMovementStart(SubmarineMovementStartEvent evt)
        {
            submarineMovements++;
            if (logDetailedFlow)
            {
                LogF8.Log($"[GameFlowValidator] 潜艇开始移动 - 步数: {evt.Steps}");
            }

            // 验证：潜艇移动应该在转盘旋转完成后
            if (wheelSpinCount > 0)
            {
                LogF8.Log($"[GameFlowValidator] ✅ 验证通过：潜艇在转盘旋转后开始移动");
            }
            else
            {
                string error = "验证失败：潜艇在转盘旋转前就开始移动";
                validationErrors.Add(error);
                flowValidationPassed = false;
                LogF8.LogError($"[GameFlowValidator] ❌ {error}");
            }
        }

        private void OnSubmarineMoveCompleted(SubmarineMoveCompletedEvent evt)
        {
            if (logDetailedFlow)
            {
                LogF8.Log($"[GameFlowValidator] 潜艇移动完成");
            }
        }

        #endregion

        #region 验证方法

        [Button("重置验证数据")]
        public void ResetValidationData()
        {
            inputCount = 0;
            lightsActivated = 0;
            wheelSpinCount = 0;
            submarineMovements = 0;
            flowValidationPassed = true;
            validationErrors.Clear();
            LogF8.Log("[GameFlowValidator] 验证数据已重置");
        }

        [Button("生成验证报告")]
        public void GenerateValidationReport()
        {
            LogF8.Log("=== 游戏流程验证报告 ===");
            LogF8.Log($"灯光激活次数: {lightsActivated}");
            LogF8.Log($"转盘旋转次数: {wheelSpinCount}");
            LogF8.Log($"潜艇移动次数: {submarineMovements}");
            LogF8.Log($"验证结果: {(flowValidationPassed ? "通过" : "失败")}");

            if (!flowValidationPassed)
            {
                LogF8.LogError("验证错误:");
                foreach (var error in validationErrors)
                {
                    LogF8.LogError($"- {error}");
                }
            }
            LogF8.Log("========================");
        }

        [Button("测试完整流程")]
        public void TestCompleteFlow()
        {
            StartCoroutine(TestCompleteFlowCoroutine());
        }

        private IEnumerator TestCompleteFlowCoroutine()
        {
            ResetValidationData();
            LogF8.Log("[GameFlowValidator] 开始测试完整流程");

            // 模拟3次输入激活所有灯光
            var inputBufferSystem = QHLCArchitecture.Interface.GetSystem<InputBufferSystem>();
            if (inputBufferSystem != null)
            {
                for (int i = 0; i < 3; i++)
                {
                    var inputEvent = new InputEvent
                    {
                        Type = InputType.LightActivation,
                        Index = -1,
                        Timestamp = Time.time
                    };
                    inputBufferSystem.RecordInput(inputEvent);
                    yield return new WaitForSeconds(1f);
                }
            }

            // 等待流程完成
            yield return new WaitForSeconds(10f);

            // 生成验证报告
            GenerateValidationReport();
        }

        [Button("验证修复后的缓存逻辑")]
        public void ValidateFixedCacheLogic()
        {
            StartCoroutine(ValidateFixedCacheLogicCoroutine());
        }

        private IEnumerator ValidateFixedCacheLogicCoroutine()
        {
            ResetValidationData();
            LogF8.Log("[GameFlowValidator] 开始验证修复后的缓存逻辑");

            // 获取转盘缓存系统
            var wheelBufferSystem = QHLCArchitecture.Interface.GetSystem<WheelBufferSystem>();
            if (wheelBufferSystem == null)
            {
                LogF8.LogError("[GameFlowValidator] 无法获取WheelBufferSystem");
                yield break;
            }

            // 记录初始缓存次数
            int initialCacheCount = wheelBufferSystem.GetBufferedSpinCount();
            LogF8.Log($"[GameFlowValidator] 初始转盘缓存次数: {initialCacheCount}");

            // 模拟3次输入激活所有灯光
            var inputBufferSystem = QHLCArchitecture.Interface.GetSystem<InputBufferSystem>();
            if (inputBufferSystem != null)
            {
                for (int i = 0; i < 3; i++)
                {
                    var inputEvent = new InputEvent
                    {
                        Type = InputType.LightActivation,
                        Index = -1,
                        Timestamp = Time.time
                    };
                    inputBufferSystem.RecordInput(inputEvent);
                    yield return new WaitForSeconds(1f);
                }
            }

            // 等待灯光动画完成
            yield return new WaitForSeconds(5f);

            // 检查缓存次数是否正确增加了1次（而不是2次）
            int finalCacheCount = wheelBufferSystem.GetBufferedSpinCount();
            int expectedCacheCount = initialCacheCount + 1;

            LogF8.Log($"[GameFlowValidator] 最终转盘缓存次数: {finalCacheCount}");
            LogF8.Log($"[GameFlowValidator] 预期转盘缓存次数: {expectedCacheCount}");

            if (finalCacheCount == expectedCacheCount)
            {
                LogF8.Log("[GameFlowValidator] ✅ 修复验证成功：转盘缓存次数正确增加了1次");
            }
            else
            {
                LogF8.LogError($"[GameFlowValidator] ❌ 修复验证失败：转盘缓存次数不正确，实际: {finalCacheCount}, 预期: {expectedCacheCount}");
                if (finalCacheCount > expectedCacheCount)
                {
                    LogF8.LogError("[GameFlowValidator] 可能仍存在重复添加缓存次数的问题");
                }
            }

            // 生成验证报告
            GenerateValidationReport();
        }

        #endregion
    }
} 