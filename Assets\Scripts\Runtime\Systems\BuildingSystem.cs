using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using QFramework;
using QHLC.Models;
using QHLC.Events;
using QHLC.Common;
using QHLC.ScriptableObjects;
using QHLC.Enums;
using F8Framework.Core;
using QHLC.Utilities;

namespace QHLC.Systems
{
    /// <summary>
    /// 建筑系统，管理建筑的创建、更新和交互
    /// </summary>
    public class BuildingSystem : AbstractSystem
    {
        protected override void OnInit()
        {
            // 注册事件
            this.RegisterEvent<SubmarineStationReachedEvent>(OnSubmarineStationReached);
            this.RegisterEvent<BuildingsInitializedEvent>(OnBuildingsInitialized);
            this.RegisterEvent<SubmarinePathInitializedEvent>(OnSubmarinePathInitialized);

            ModuleLogManager.Log("BuildingSystem: 初始化完成，等待路径点初始化事件");
        }

        /// <summary>
        /// 处理潜艇路径初始化事件
        /// </summary>
        /// <param name="e">事件数据</param>
        private void OnSubmarinePathInitialized(SubmarinePathInitializedEvent e)
        {
            ModuleLogManager.Log($"BuildingSystem: 收到路径初始化事件，路径点数量: {e.PathItems?.Count ?? 0}");
            
            if (e.PathItems != null && e.PathItems.Count > 0)
            {
                // 路径点已经初始化，可以继续后续的建筑系统初始化
                ModuleLogManager.Log($"BuildingSystem: 路径初始化完成，共 {e.PathItems.Count} 个路径点");
            }
            else
            {
                ModuleLogManager.LogError("BuildingSystem: 收到的路径点数据为空");
            }
        }

        /// <summary>
        /// 处理建筑初始化完成事件
        /// </summary>
        /// <param name="e">事件数据</param>
        private void OnBuildingsInitialized(BuildingsInitializedEvent e)
        {
            // 发送建筑创建事件
            var buildingModel = this.GetModel<BuildingModel>();
            var buildings = buildingModel.GetAllBuildings();

            for (int i = 0; i < buildings.Count; i++)
            {
                this.SendEvent(new BuildingCreatedEvent { Building = buildings[i] });
            }

            ModuleLogManager.Log($"BuildingSystem: 发送了 {buildings.Count} 个建筑创建事件");
        }

        /// <summary>
        /// 处理潜艇到达站点事件
        /// </summary>
        /// <param name="e">事件数据</param>
        private void OnSubmarineStationReached(SubmarineStationReachedEvent e)
        {
            var buildingModel = this.GetModel<BuildingModel>();
            var building = buildingModel.GetBuildingByStationIndex(e.StationIndex);

            if (building != null && building.IsActive)
            {
                // 设置当前激活的建筑
                buildingModel.SetCurrentActiveBuilding(building);

                // 获取建筑的奖励
                var rewardModel = this.GetModel<RewardModel>();
                var rewards = rewardModel.GetRewardsByBuildingId(building.Id);

                // 设置当前可收集的奖励
                var collectableRewards = rewards.Where(r => !r.IsCollected).ToList();
                rewardModel.SetCurrentCollectableRewards(collectableRewards);

                // 发送建筑交互事件
                this.SendEvent(new BuildingInteractionEvent { Building = building, Rewards = collectableRewards });

                ModuleLogManager.Log($"BuildingSystem: 潜艇到达站点 {e.StationIndex}，激活建筑 {building.Id}，可收集奖励 {collectableRewards.Count} 个");
            }
        }
    }
} 