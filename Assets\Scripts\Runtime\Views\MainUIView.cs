using UnityEngine;
using UnityEngine.UI;
using TMPro;
using QFramework;
using F8Framework.Core;
using QHLC;
using QHLC.Models;
using QHLC.Views;
using QHLC.FSM;
using QHLC.Controllers;
using QHLC.Events;
using QHLC.Prize;

public class MainUIView : Mono<PERSON><PERSON><PERSON><PERSON>, IController
{
    [Header("视图组件")]
    [SerializeField] private LightView lightView;
    [SerializeField] private SubmarineView submarineView;
    [SerializeField] private WheelView wheelView;
    [SerializeField] private PrizeInfoView prizeInfoView;

    [Header("教程界面")]
    [SerializeField] private GameObject tutorialPanel; // 教程面板
    [SerializeField] private Button startButton; // 开始按钮

    [Header("UI元素")]
    [SerializeField] private Text wheelBufferCountText; // 转盘缓存次数文本

    [Header("奖励UI位置")]
    [SerializeField] private RectTransform coinUI; // 金币UI位置
    [SerializeField] private RectTransform lotteryUI; // 彩票UI位置
    [SerializeField] private RectTransform wheelUI; // 转盘UI位置
    [SerializeField] private RectTransform gachaUI; // 扭蛋UI位置
    [SerializeField] private RectTransform jpStarUI; // JP大奖UI位置

    // 游戏模型
    private GameModel gameModel;

    /// <summary>
    /// 获取架构
    /// </summary>
    public IArchitecture GetArchitecture()
    {
        return QHLCArchitecture.Interface;
    }

    private void Update()
    {
        HandleInput();
    }

    // Awake
    private void Awake()
    {
        RegisterGameController();

        // 初始化游戏模型
        gameModel = new GameModel();

        // 使用 QFramework 注册事件监听
        // 注册转盘停止事件
        TypeEventSystem.Global.Register<QHLC.Events.WheelStoppedEvent>(OnWheelStoppedHandler);

        // 移除转盘闪烁完成事件监听，让GameFSMCoordinator统一处理
        // TypeEventSystem.Global.Register<QHLC.Events.WheelFlashingCompletedEvent>(OnWheelFlashingCompleted);

        // 注册潜艇移动完成事件
        TypeEventSystem.Global.Register<QHLC.Events.SubmarineMoveCompletedEvent>(OnSubmarineMoveCompletedHandler);

        // 删除灯光全部激活事件的监听，因为它已由状态机处理

        // 注册灯光激活事件
        TypeEventSystem.Global.Register<QHLC.Events.LightActivatedEvent>(OnLightActivatedHandler);

        // 注册转盘缓存次数变化事件
        TypeEventSystem.Global.Register<QHLC.Events.WheelBufferCountChangedEvent>(OnWheelBufferCountChangedHandler);

        // 初始化转盘缓存次数显示
        UpdateWheelBufferCountUI(0);
    }
    private void RegisterGameController()
    {
        GameController.Instance.lightView = lightView;
        GameController.Instance.wheelView = wheelView;
        GameController.Instance.submarineView = submarineView;
    }
    // 参数传入，每次打开UI都会执行
    private void OnEnable()
    {
    }

    // Start
    private void Start()
    {
    }

    // 打开界面动画完成后
    private void ShowUI()
    {
        // 显示教程面板
        if (tutorialPanel != null)
        {
            tutorialPanel.SetActive(true);
        }

        // 设置开始按钮点击事件
        if (startButton != null)
        {
            startButton.onClick.AddListener(() =>
            {
                if (tutorialPanel != null)
                {
                    tutorialPanel.SetActive(false);
                }
                StartGame();
            });
        }
        else
        {
            // 如果没有开始按钮，直接开始游戏
            StartGame();
        }
    }

    // 开始游戏
    public void StartGame()
    {
        // 重置游戏状态
        gameModel.ResetGame();

        // 重置视图
        if (lightView != null)
        {
            lightView.ResetLights();
        }

        if (wheelView != null)
        {
            // 使用QFramework架构发送重置命令
            this.SendCommand<ResetWheelCommand>();
        }

        if (submarineView != null)
        {
            // 使用QFramework架构发送重置命令
            this.SendCommand<ResetSubmarineCommand>();
        }

        // 设置游戏状态为灯光激活阶段
        // 游戏状态由FSM管理，不再使用 gameModel.CurrentState
    }

    // 开始灯光激活阶段 - 这个方法已不再使用，保留以避免编译错误
    private void StartLightFlashing()
    {
        // 这个方法已不再使用，因为当转盘结果为Stop时，我们直接发送SubmarineMoveCompletedEvent事件
        LogF8.LogWarning("MainUIView: StartLightFlashing方法已不再使用");
    }

    // 处理用户输入
    private void HandleInput()
    {
        // 处理空格键输入
        if (Input.GetKeyDown(KeyCode.Space))
        {
            // 创建输入事件
            InputEvent inputEvent = new InputEvent
            {
                Type = InputType.LightActivation,
                Index = -1 // 使用-1表示随机灯光
            };

            // 记录输入
            var inputBufferSystem = this.GetSystem<InputBufferSystem>();
            if (inputBufferSystem != null)
            {
                inputBufferSystem.RecordInput(inputEvent);
            }
            else if (lightView != null)
            {
                // 如果无法获取InputBufferSystem，直接调用lightView
                lightView.OnSpaceKeyDown();
            }
        }
    }

    // 删除所有灯光激活事件处理，因为它已由状态机处理

    // 灯光激活事件处理
    private void OnLightActivatedHandler(QHLC.Events.LightActivatedEvent e)
    {
        // 更新灯光状态
        lightView.ShowLightAtIndex(e.LightIndex);
    }

    // 转盘停止事件处理
    private void OnWheelStoppedHandler(QHLC.Events.WheelStoppedEvent e)
    {
        // 简化处理，只记录日志，不再缓存结果
        // 潜艇移动现在由GameFSMCoordinator统一协调
        LogF8.Log($"MainUIView: 转盘停止，结果：{(int)e.Result} ({e.Result})，潜艇移动将由GameFSMCoordinator处理");
    }

    // 潜艇移动完成事件处理
    private void OnSubmarineMoveCompletedHandler(SubmarineMoveCompletedEvent e)
    {
        // 更新模型中的潜艇位置
        var submarineModel = this.GetModel<SubmarineModel>();
        gameModel.SubmarinePosition = submarineModel.CurrentStationIndex.Value;
        LogF8.Log($"MainUIView: 潜艇移动完成，当前站点索引：{gameModel.SubmarinePosition}");

        // 潜艇移动完成后，状态切换由FSM自动处理
        // 不需要手动调用StartLightFlashing方法
    }

    // 删除，UI关闭后调用
    private void OnDestroy()
    {
        // 移除事件监听
        TypeEventSystem.Global.UnRegister<QHLC.Events.WheelStoppedEvent>(OnWheelStoppedHandler);
        // 移除转盘闪烁完成事件的注销，因为我们不再监听这个事件
        // TypeEventSystem.Global.UnRegister<QHLC.Events.WheelFlashingCompletedEvent>(OnWheelFlashingCompleted);
        TypeEventSystem.Global.UnRegister<QHLC.Events.SubmarineMoveCompletedEvent>(OnSubmarineMoveCompletedHandler);
        // 删除灯光全部激活事件的注销
        TypeEventSystem.Global.UnRegister<QHLC.Events.LightActivatedEvent>(OnLightActivatedHandler);
        // 移除转盘缓存次数变化事件的监听
        TypeEventSystem.Global.UnRegister<QHLC.Events.WheelBufferCountChangedEvent>(OnWheelBufferCountChangedHandler);
    }

    // 转盘缓存次数变化事件处理
    private void OnWheelBufferCountChangedHandler(WheelBufferCountChangedEvent e)
    {
        // 更新UI显示
        UpdateWheelBufferCountUI(e.Count);
    }

    // 更新转盘缓存次数UI
    private void UpdateWheelBufferCountUI(int count)
    {
        if (wheelBufferCountText != null)
        {
            wheelBufferCountText.text = $"{count}";

            // 如果没有缓存的转盘次数，隐藏UI
            wheelBufferCountText.gameObject.SetActive(count > 0);
        }
    }

    // 获取UI位置方法

    /// <summary>
    /// 获取金币UI的位置
    /// </summary>
    /// <returns>金币UI的世界坐标</returns>
    public Vector3 GetCoinUIPosition()
    {
        if (coinUI != null)
        {
            return coinUI.position;
        }
        return Vector3.zero;
    }

    /// <summary>
    /// 获取彩票UI的位置
    /// </summary>
    /// <returns>彩票UI的世界坐标</returns>
    public Vector3 GetLotteryUIPosition()
    {
        if (lotteryUI != null)
        {
            return lotteryUI.position;
        }
        return Vector3.zero;
    }

    /// <summary>
    /// 获取转盘UI的位置
    /// </summary>
    /// <returns>转盘UI的世界坐标</returns>
    public Vector3 GetWheelUIPosition()
    {
        if (wheelUI != null)
        {
            return wheelUI.position;
        }
        return Vector3.zero;
    }

    /// <summary>
    /// 获取扭蛋UI的位置
    /// </summary>
    /// <returns>扭蛋UI的世界坐标</returns>
    public Vector3 GetGachaUIPosition()
    {
        if (gachaUI != null)
        {
            return gachaUI.position;
        }
        return Vector3.zero;
    }

    /// <summary>
    /// 获取JP大奖UI的位置
    /// </summary>
    /// <returns>JP大奖UI的世界坐标</returns>
    public Vector3 GetJpStarUIPosition()
    {
        if (jpStarUI != null)
        {
            return jpStarUI.position;
        }
        return Vector3.zero;
    }

    // 自动获取组件（自动生成，不能删除）
}
