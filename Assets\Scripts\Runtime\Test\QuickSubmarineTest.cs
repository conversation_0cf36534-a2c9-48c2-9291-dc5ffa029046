using UnityEngine;
using QHLC.Managers;
using QHLC.Utilities;

namespace QHLC.Test
{
    /// <summary>
    /// 快速潜艇测试脚本
    /// 用于验证SubmarineManager的基本功能
    /// </summary>
    public class QuickSubmarineTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private KeyCode testMoveKey = KeyCode.T;
        [SerializeField] private KeyCode testResetKey = KeyCode.Y;
        [SerializeField] private int moveSteps = 1;

        private void Start()
        {
            ModuleLogManager.LogSubmarine("QuickSubmarineTest: 测试脚本启动");
            
            // 检查SubmarineManager是否可用
            if (SubmarineManager.Instance != null)
            {
                ModuleLogManager.LogSubmarine("QuickSubmarineTest: SubmarineManager实例已创建");
            }
            else
            {
                ModuleLogManager.LogSubmarineError("QuickSubmarineTest: SubmarineManager实例创建失败");
            }
        }

        private void Update()
        {
            // T键测试移动
            if (Input.GetKeyDown(testMoveKey))
            {
                TestMove();
            }

            // Y键测试重置
            if (Input.GetKeyDown(testResetKey))
            {
                TestReset();
            }

            // 显示帮助信息
            if (Input.GetKeyDown(KeyCode.H))
            {
                ShowHelp();
            }
        }

        private void TestMove()
        {
            ModuleLogManager.LogSubmarine($"QuickSubmarineTest.TestMove: 测试移动，步数: {moveSteps}");

            if (SubmarineManager.Instance == null)
            {
                ModuleLogManager.LogSubmarineError("QuickSubmarineTest: SubmarineManager未找到");
                return;
            }

            if (SubmarineManager.Instance.IsMoving())
            {
                ModuleLogManager.LogSubmarineWarning("QuickSubmarineTest: 潜艇正在移动中，请等待");
                return;
            }

            SubmarineManager.Instance.MoveSubmarine(moveSteps, () => {
                ModuleLogManager.LogSubmarine("QuickSubmarineTest: 移动完成回调");
                ShowStatus();
            });
        }

        private void TestReset()
        {
            ModuleLogManager.LogSubmarine("QuickSubmarineTest.TestReset: 测试重置");

            if (SubmarineManager.Instance == null)
            {
                ModuleLogManager.LogSubmarineError("QuickSubmarineTest: SubmarineManager未找到");
                return;
            }

            SubmarineManager.Instance.ResetSubmarinePosition();
            ShowStatus();
        }

        private void ShowStatus()
        {
            if (SubmarineManager.Instance != null)
            {
                var manager = SubmarineManager.Instance;
                ModuleLogManager.LogSubmarine($"潜艇状态 - 站点: {manager.GetCurrentStationIndex()}, " +
                    $"移动中: {manager.IsMoving()}, " +
                    $"路径点: {manager.GetPathItemCount()}");
            }
        }

        private void ShowHelp()
        {
            ModuleLogManager.LogSubmarine("QuickSubmarineTest 帮助:\n" +
                $"按 {testMoveKey} 键 - 移动潜艇\n" +
                $"按 {testResetKey} 键 - 重置潜艇\n" +
                "按 H 键 - 显示帮助");
        }

        private void OnGUI()
        {
            // 显示简单的状态信息
            GUILayout.BeginArea(new Rect(10, 10, 300, 150));
            GUILayout.Label("=== 潜艇快速测试 ===");
            GUILayout.Label($"按 {testMoveKey} 键移动潜艇");
            GUILayout.Label($"按 {testResetKey} 键重置潜艇");
            GUILayout.Label("按 H 键显示帮助");
            
            GUILayout.Space(10);
            
            if (SubmarineManager.Instance != null)
            {
                var manager = SubmarineManager.Instance;
                GUILayout.Label($"当前站点: {manager.GetCurrentStationIndex()}");
                GUILayout.Label($"移动状态: {(manager.IsMoving() ? "移动中" : "静止")}");
                GUILayout.Label($"路径点数: {manager.GetPathItemCount()}");
            }
            else
            {
                GUILayout.Label("SubmarineManager: 未找到");
            }
            
            GUILayout.EndArea();
        }
    }
} 