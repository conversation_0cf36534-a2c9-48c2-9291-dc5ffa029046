using System.Collections;
using UnityEngine;
using QFramework;
using QHLC.Controllers;
using QHLC.Models;
using QHLC.Events;
using QHLC.FSM.Wheel;
using QHLC.FSM.HLight;
using QHLC.Views;
using F8Framework.Core;

namespace QHLC.FSM
{
    /// <summary>
    /// HFSM测试控制器，用于测试整个状态机流程的切换
    /// 提供完整的游戏流程测试，包括灯光激活、转盘旋转、潜艇移动等
    /// </summary>
    public class HFSMTestController : MonoBehaviour, IController
    {
        #region 序列化字段
        [Header("测试配置")]
        [SerializeField] private bool autoTest = false;
        [SerializeField] private float autoTestInterval = 3f;
        [SerializeField] private bool enableDebugLog = true;
        
        [Header("组件引用")]
        [SerializeField] private GameController gameController;
        [SerializeField] private WheelView wheelView;
        [SerializeField] private LightView lightView;
        #endregion

        #region 私有字段
        private GameFSMCoordinator gameFSMCoordinator;
        private WheelFSM wheelFSM;
        private LightHFSM lightFSM;
        private bool isInitialized = false;
        private Coroutine autoTestCoroutine;
        private int testStep = 0;
        #endregion

        #region Unity生命周期
        private void Awake()
        {
            // 注册事件监听
            this.RegisterEvent<GameStateChangedEvent>(OnGameStateChanged);
            this.RegisterEvent<FSMStateChangedEvent>(OnFSMStateChanged);
            this.RegisterEvent<WheelStoppedEvent>(OnWheelStopped);
            this.RegisterEvent<LightActivatedEvent>(OnLightActivated);
        }

        private void Start()
        {
            StartCoroutine(InitializeWithDelay());
        }

        private void Update()
        {
            HandleInput();
        }

        private void OnDestroy()
        {
            // 停止自动测试
            if (autoTestCoroutine != null)
            {
                StopCoroutine(autoTestCoroutine);
            }

            // 注销事件监听
            this.UnRegisterEvent<GameStateChangedEvent>(OnGameStateChanged);
            this.UnRegisterEvent<FSMStateChangedEvent>(OnFSMStateChanged);
            this.UnRegisterEvent<WheelStoppedEvent>(OnWheelStopped);
            this.UnRegisterEvent<LightActivatedEvent>(OnLightActivated);
        }
        #endregion

        #region 初始化
        /// <summary>
        /// 延迟初始化，确保所有组件都已准备就绪
        /// </summary>
        private IEnumerator InitializeWithDelay()
        {
            yield return new WaitForSeconds(0.1f);
            Initialize();
        }

        /// <summary>
        /// 初始化测试控制器
        /// </summary>
        public void Initialize()
        {
            if (isInitialized)
            {
                LogDebug("HFSMTestController: 已经初始化过，跳过重复初始化");
                return;
            }

            // 查找必要的组件
            FindRequiredComponents();

            // 验证组件完整性
            if (!ValidateComponents())
            {
                LogF8.LogError("HFSMTestController: 组件验证失败，无法初始化");
                return;
            }

            // 初始化FSM协调器
            if (gameFSMCoordinator != null)
            {
                gameFSMCoordinator.Initialize();
            }

            isInitialized = true;
            LogDebug("HFSMTestController: 初始化完成");

            // 如果启用自动测试，开始自动测试流程
            if (autoTest)
            {
                StartAutoTest();
            }

            // 显示测试说明
            ShowTestInstructions();
        }

        /// <summary>
        /// 查找必要的组件
        /// </summary>
        private void FindRequiredComponents()
        {
            // 查找GameController
            if (gameController == null)
            {
                gameController = FindObjectOfType<GameController>();
            }

            // 查找GameFSMCoordinator
            if (gameFSMCoordinator == null)
            {
                gameFSMCoordinator = FindObjectOfType<GameFSMCoordinator>();
            }

            // 查找WheelView
            if (wheelView == null)
            {
                wheelView = FindObjectOfType<WheelView>();
            }

            // 查找LightView
            if (lightView == null)
            {
                lightView = FindObjectOfType<LightView>();
            }

            // 获取FSM组件
            if (wheelView != null)
            {
                wheelFSM = wheelView.GetComponent<WheelFSM>();
            }

            if (lightView != null)
            {
                lightFSM = lightView.GetComponent<LightHFSM>();
            }
        }

        /// <summary>
        /// 验证组件完整性
        /// </summary>
        private bool ValidateComponents()
        {
            bool isValid = true;

            if (gameController == null)
            {
                LogF8.LogError("HFSMTestController: GameController未找到");
                isValid = false;
            }

            if (gameFSMCoordinator == null)
            {
                LogF8.LogError("HFSMTestController: GameFSMCoordinator未找到");
                isValid = false;
            }

            if (wheelView == null)
            {
                LogF8.LogError("HFSMTestController: WheelView未找到");
                isValid = false;
            }

            if (lightView == null)
            {
                LogF8.LogError("HFSMTestController: LightView未找到");
                isValid = false;
            }

            if (wheelFSM == null)
            {
                LogF8.LogError("HFSMTestController: WheelFSM未找到");
                isValid = false;
            }

            return isValid;
        }
        #endregion

        #region 输入处理
        /// <summary>
        /// 处理用户输入
        /// </summary>
        private void HandleInput()
        {
            if (!isInitialized) return;

            // 数字键1-5：测试不同的游戏流程步骤
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                TestStep1_StartGame();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                TestStep2_ActivateLight();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                TestStep3_SpinWheel();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha4))
            {
                TestStep4_MoveSubmarine();
            }
            else if (Input.GetKeyDown(KeyCode.Alpha5))
            {
                TestStep5_ResetGame();
            }

            // 空格键：激活下一个灯光
            if (Input.GetKeyDown(KeyCode.Space))
            {
                ActivateNextLight();
            }

            // W键：开始转盘旋转
            if (Input.GetKeyDown(KeyCode.W))
            {
                StartWheelSpin(WheelResult.Move1);
            }

            // A/S/D键：不同的转盘结果
            if (Input.GetKeyDown(KeyCode.A))
            {
                StartWheelSpin(WheelResult.Move2);
            }
            else if (Input.GetKeyDown(KeyCode.S))
            {
                StartWheelSpin(WheelResult.Move3);
            }
            else if (Input.GetKeyDown(KeyCode.D))
            {
                StartWheelSpin(WheelResult.Move4);
            }

            // R键：重置所有状态机
            if (Input.GetKeyDown(KeyCode.R))
            {
                ResetAllFSMs();
            }

            // T键：切换自动测试
            if (Input.GetKeyDown(KeyCode.T))
            {
                ToggleAutoTest();
            }
        }
        #endregion

        #region 测试步骤
        /// <summary>
        /// 测试步骤1：开始游戏
        /// </summary>
        public void TestStep1_StartGame()
        {
            LogDebug("=== 测试步骤1：开始游戏 ===");
            
            if (gameFSMCoordinator != null)
            {
                gameFSMCoordinator.StartGame();
            }
            else
            {
                LogF8.LogError("GameFSMCoordinator未找到，无法开始游戏");
            }
        }

        /// <summary>
        /// 测试步骤2：激活灯光
        /// </summary>
        public void TestStep2_ActivateLight()
        {
            LogDebug("=== 测试步骤2：激活灯光 ===");
            ActivateNextLight();
        }

        /// <summary>
        /// 测试步骤3：旋转转盘
        /// </summary>
        public void TestStep3_SpinWheel()
        {
            LogDebug("=== 测试步骤3：旋转转盘 ===");
            StartWheelSpin(WheelResult.Move1);
        }

        /// <summary>
        /// 测试步骤4：移动潜艇
        /// </summary>
        public void TestStep4_MoveSubmarine()
        {
            LogDebug("=== 测试步骤4：移动潜艇 ===");
            // 这里应该触发潜艇移动，但目前潜艇FSM还未完全实现
            LogDebug("潜艇移动功能待实现");
        }

        /// <summary>
        /// 测试步骤5：重置游戏
        /// </summary>
        public void TestStep5_ResetGame()
        {
            LogDebug("=== 测试步骤5：重置游戏 ===");
            ResetAllFSMs();
        }
        #endregion

        #region 具体操作
        /// <summary>
        /// 激活下一个灯光
        /// </summary>
        public void ActivateNextLight()
        {
            if (lightView == null) return;

            var inputBufferSystem = lightView.GetArchitecture().GetSystem<InputBufferSystem>();
            if (inputBufferSystem != null)
            {
                var inputEvent = new InputEvent
                {
                    Type = InputType.LightActivation,
                    Index = -1, // -1表示激活下一个未激活的灯光
                    Timestamp = Time.time
                };
                inputBufferSystem.RecordInput(inputEvent);
                LogDebug("记录灯光激活输入");
            }
        }

        /// <summary>
        /// 开始转盘旋转
        /// </summary>
        public void StartWheelSpin(WheelResult result)
        {
            if (wheelFSM != null)
            {
                wheelFSM.StartSpinning(result);
                LogDebug($"开始转盘旋转，目标结果: {result}");
            }
            else
            {
                LogF8.LogError("WheelFSM未找到，无法开始旋转");
            }
        }

        /// <summary>
        /// 重置所有状态机
        /// </summary>
        public void ResetAllFSMs()
        {
            LogDebug("重置所有状态机");

            // 重置WheelFSM
            if (wheelFSM != null)
            {
                wheelFSM.ResetWheel();
            }

            // 重置LightFSM
            if (lightFSM != null)
            {
                lightFSM.ChangeState(LightStateType.Idle);
            }

            // 重置游戏状态
            if (gameFSMCoordinator != null)
            {
                gameFSMCoordinator.ChangeState(GameStateType.Idle);
            }

            testStep = 0;
        }
        #endregion

        #region 自动测试
        /// <summary>
        /// 开始自动测试
        /// </summary>
        public void StartAutoTest()
        {
            if (autoTestCoroutine != null)
            {
                StopCoroutine(autoTestCoroutine);
            }

            autoTestCoroutine = StartCoroutine(AutoTestCoroutine());
            LogDebug("开始自动测试流程");
        }

        /// <summary>
        /// 停止自动测试
        /// </summary>
        public void StopAutoTest()
        {
            if (autoTestCoroutine != null)
            {
                StopCoroutine(autoTestCoroutine);
                autoTestCoroutine = null;
            }
            LogDebug("停止自动测试流程");
        }

        /// <summary>
        /// 切换自动测试
        /// </summary>
        public void ToggleAutoTest()
        {
            autoTest = !autoTest;
            
            if (autoTest)
            {
                StartAutoTest();
            }
            else
            {
                StopAutoTest();
            }
            
            LogDebug($"自动测试: {(autoTest ? "开启" : "关闭")}");
        }

        /// <summary>
        /// 自动测试协程
        /// </summary>
        private IEnumerator AutoTestCoroutine()
        {
            while (autoTest)
            {
                switch (testStep)
                {
                    case 0:
                        TestStep1_StartGame();
                        testStep++;
                        break;
                    case 1:
                        TestStep2_ActivateLight();
                        testStep++;
                        break;
                    case 2:
                        TestStep3_SpinWheel();
                        testStep++;
                        break;
                    case 3:
                        TestStep4_MoveSubmarine();
                        testStep++;
                        break;
                    case 4:
                        TestStep5_ResetGame();
                        testStep = 0; // 重新开始循环
                        break;
                }

                yield return new WaitForSeconds(autoTestInterval);
            }
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 处理游戏状态变化事件
        /// </summary>
        private void OnGameStateChanged(GameStateChangedEvent evt)
        {
            LogDebug($"游戏状态变化: {evt.PreviousState} -> {evt.CurrentState}");
        }

        /// <summary>
        /// 处理FSM状态变化事件
        /// </summary>
        private void OnFSMStateChanged(FSMStateChangedEvent evt)
        {
            LogDebug($"FSM状态变化 [{evt.FSMType}]: {evt.PreviousState} -> {evt.CurrentState}");
        }

        /// <summary>
        /// 处理转盘停止事件
        /// </summary>
        private void OnWheelStopped(WheelStoppedEvent evt)
        {
            LogDebug($"转盘停止，结果: {evt.Result}");
        }

        /// <summary>
        /// 处理灯光激活事件
        /// </summary>
        private void OnLightActivated(LightActivatedEvent evt)
        {
            LogDebug($"灯光激活，索引: {evt.LightIndex}");
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 显示测试说明
        /// </summary>
        private void ShowTestInstructions()
        {
            LogDebug("=== HFSM测试控制器使用说明 ===");
            LogDebug("数字键1-5: 执行对应的测试步骤");
            LogDebug("空格键: 激活下一个灯光");
            LogDebug("W键: 转盘旋转(Move1)");
            LogDebug("A/S/D键: 转盘旋转(Move2/Move3/Move4)");
            LogDebug("R键: 重置所有状态机");
            LogDebug("T键: 切换自动测试模式");
            LogDebug("================================");
        }

        /// <summary>
        /// 调试日志输出
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                LogF8.Log($"[HFSMTest] {message}");
            }
        }

        /// <summary>
        /// 获取架构
        /// </summary>
        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }
        #endregion
    }
} 