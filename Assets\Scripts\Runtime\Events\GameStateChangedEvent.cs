using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QHLC.FSM;

namespace QHLC.Events
{
    /// <summary>
    /// 游戏状态变化事件
    /// </summary>
    public class GameStateChangedEvent
    {
        /// <summary>
        /// 前一个状态
        /// </summary>
        public GameStateType PreviousState;

        /// <summary>
        /// 当前状态
        /// </summary>
        public GameStateType CurrentState;
    }
}
