using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QHLC.Controllers;
using QHLC.Models;
using QHLC.Views;
using QHLC.Managers;
using F8Framework.Launcher;
using QFramework;
using QHLC.Events;
using System;
using QHLC.Utilities;
using DG.Tweening;
using Sequence = DG.Tweening.Sequence;

namespace QHLC.FSM
{
    /// <summary>
    /// 潜艇闲置状态 - 处理潜艇的闲置动画
    /// </summary>
    public class SubmarineIdleHState : AbstractHState<SubmarineStateType, SubmarineView>
    {
        private Sequence idleSequence;

        public SubmarineIdleHState(HFSM<SubmarineStateType> fsm, SubmarineView target) : base(fsm, target)
        {
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogSubmarine("SubmarineIdleHState: 进入闲置状态");

            // 🔧 以QuickSubmarineTest为准：添加状态同步检查
            if (SubmarineManager.Instance != null)
            {
                ModuleLogManager.LogSubmarine($"SubmarineIdleHState.OnEnter: 当前SubmarineManager状态 - 站点: {SubmarineManager.Instance.GetCurrentStationIndex()}, 移动中: {SubmarineManager.Instance.IsMoving()}");
                
                // 确保与QuickSubmarineTest的行为一致
                if (SubmarineManager.Instance.IsMoving())
                {
                    ModuleLogManager.LogSubmarineWarning("SubmarineIdleHState.OnEnter: 注意！进入闲置状态时，SubmarineManager仍处于移动中");
                }
            }

            // 启动闲置动画序列
            StartIdleAnimation();
        }

        protected override void OnUpdate()
        {
            // 闲置状态的更新逻辑
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogSubmarine("SubmarineIdleHState: 退出闲置状态");

            // 停止闲置动画
            StopIdleAnimation();
        }

        private void StartIdleAnimation()
        {
            if (mTarget == null) return;

            // 创建闲置动画序列
            idleSequence = DOTween.Sequence();

            // 添加上下浮动动画
            idleSequence.Append(mTarget.transform.DOLocalMoveY(mTarget.transform.localPosition.y + 0.5f, 2f))
                       .Append(mTarget.transform.DOLocalMoveY(mTarget.transform.localPosition.y - 0.5f, 2f))
                       .SetLoops(-1, LoopType.Yoyo)
                       .SetEase(DG.Tweening.Ease.InOutSine);
        }

        private void StopIdleAnimation()
        {
            idleSequence?.Kill();
            idleSequence = null;
        }
    }

    /// <summary>
    /// 潜艇移动状态 - 处理潜艇的移动逻辑
    /// </summary>
    public class SubmarineMovingHState : AbstractHState<SubmarineStateType, SubmarineView>
    {
        public SubmarineMovingHState(HFSM<SubmarineStateType> fsm, SubmarineView target) : base(fsm, target)
        {
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogSubmarine("SubmarineMovingHState: 进入移动状态");

            // 🔧 以QuickSubmarineTest为准：添加状态同步检查
            if (SubmarineManager.Instance != null)
            {
                ModuleLogManager.LogSubmarine($"SubmarineMovingHState.OnEnter: 当前SubmarineManager状态 - 站点: {SubmarineManager.Instance.GetCurrentStationIndex()}, 移动中: {SubmarineManager.Instance.IsMoving()}");
                
                // 确保与QuickSubmarineTest的行为一致
                if (SubmarineManager.Instance.IsMoving())
                {
                    ModuleLogManager.LogSubmarineWarning("SubmarineMovingHState.OnEnter: 注意！进入移动状态时，SubmarineManager已处于移动中");
                }
            }
            else
            {
                ModuleLogManager.LogSubmarineError("SubmarineMovingHState.OnEnter: SubmarineManager未找到");
            }

            // 只注册移动步骤完成事件，移动事件由SubmarineFSM统一处理
            TypeEventSystem.Global.Register<SubmarineStepCompletedEvent>(OnSubmarineStepCompleted);
        }

        protected override void OnUpdate()
        {
            // 移动状态的更新逻辑
            // 在这个状态下，移动逻辑由SubmarineManager处理
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogSubmarine("SubmarineMovingHState: 退出移动状态");

            // 取消注册事件
            TypeEventSystem.Global.UnRegister<SubmarineStepCompletedEvent>(OnSubmarineStepCompleted);
        }

        private void OnSubmarineStepCompleted(SubmarineStepCompletedEvent evt)
        {
            ModuleLogManager.LogSubmarine($"SubmarineMovingHState: 移动步骤完成，当前步数: {evt.CurrentStep}/{evt.TotalSteps}");

            // 如果是最后一步，切换到到达状态
            if (evt.IsLastStep)
            {
                ModuleLogManager.LogSubmarine("SubmarineMovingHState: 最后一步完成，通过GameFSMCoordinator请求切换到到达状态");
                
                // 通过GameFSMCoordinator请求状态切换
                if (GameFSMCoordinator.Instance != null)
                {
                    GameFSMCoordinator.Instance.RequestSubmarineStateChange(SubmarineStateType.Arrived);
                }
                else
                {
                    ModuleLogManager.LogSubmarine("SubmarineMovingHState: GameFSMCoordinator单例未找到，直接切换状态");
                    mFSM.ChangeState(SubmarineStateType.Arrived);
                }
            }
        }
    }

    /// <summary>
    /// 潜艇到达状态 - 处理潜艇到达目标位置后的逻辑
    /// </summary>
    public class SubmarineArrivedHState : AbstractHState<SubmarineStateType, SubmarineView>
    {
        private float arrivalTimer = 0f;
        private const float ARRIVAL_DISPLAY_TIME = 2f; // 到达状态显示时间
        private bool hasRequestedStateChange = false; // 添加标志位防止重复调用

        public SubmarineArrivedHState(HFSM<SubmarineStateType> fsm, SubmarineView target) : base(fsm, target)
        {
        }

        protected override void OnEnter()
        {
            ModuleLogManager.LogSubmarine("SubmarineArrivedHState: 进入到达状态");

            // 🔧 以QuickSubmarineTest为准：添加状态同步检查
            if (SubmarineManager.Instance != null)
            {
                ModuleLogManager.LogSubmarine($"SubmarineArrivedHState.OnEnter: 当前SubmarineManager状态 - 站点: {SubmarineManager.Instance.GetCurrentStationIndex()}, 移动中: {SubmarineManager.Instance.IsMoving()}");
                
                // 确保与QuickSubmarineTest的行为一致
                if (SubmarineManager.Instance.IsMoving())
                {
                    ModuleLogManager.LogSubmarineWarning("SubmarineArrivedHState.OnEnter: 注意！进入到达状态时，SubmarineManager仍处于移动中，这可能表示状态不同步");
                }
            }

            arrivalTimer = 0f;
            hasRequestedStateChange = false; // 重置防重复调用标志

            // 播放到达动画或效果
            PlayArrivalEffect();

            // 发送潜艇移动完成事件
            TypeEventSystem.Global.Send(new SubmarineMoveCompletedEvent());
        }

        protected override void OnUpdate()
        {
            arrivalTimer += Time.deltaTime;

            // 在到达状态停留一段时间后自动切换回闲置状态
            // 只有在时间到达且还没有请求过状态切换时才执行
            if (arrivalTimer >= ARRIVAL_DISPLAY_TIME && !hasRequestedStateChange)
            {
                ModuleLogManager.LogSubmarine("SubmarineArrivedHState: 到达状态显示时间结束，通过GameFSMCoordinator请求切换到闲置状态");
                hasRequestedStateChange = true; // 设置标志位防止重复调用
                
                // 通过GameFSMCoordinator请求状态切换
                if (GameFSMCoordinator.Instance != null)
                {
                    GameFSMCoordinator.Instance.RequestSubmarineStateChange(SubmarineStateType.Idle);
                }
                else
                {
                    ModuleLogManager.LogSubmarine("SubmarineArrivedHState: GameFSMCoordinator单例未找到，直接切换状态");
                    mFSM.ChangeState(SubmarineStateType.Idle);
                }
            }
        }

        protected override void OnExit()
        {
            ModuleLogManager.LogSubmarine("SubmarineArrivedHState: 退出到达状态");
        }

        private void PlayArrivalEffect()
        {
            if (mTarget == null) return;

            // 播放到达特效
            // 例如：缩放动画、粒子效果等
            mTarget.transform.DOPunchScale(Vector3.one * 0.2f, 0.5f, 10, 1f);
        }
    }
}